import Bull from 'bull';
import { RedisService } from './redis';
import sharp from 'sharp';
import { S3Service } from './s3';
import { OpenAIService, type VisionAnalysisResult, type ProcessingOptions } from './openai';
import { AnalysisStorageService } from './analysis-storage';

interface ImageProcessingJobData {
  imageId: string;
  userId: string;
  s3Key: string;
  s3Bucket: string;
  mode: string;
  settings?: any;
}

// Multi-stage processing job interfaces
interface ImagePreprocessingJobData {
  imageId: string;
  userId: string;
  s3Key: string;
  s3Bucket: string;
  mode: string;
  settings?: any;
}

interface ComponentDetectionJobData {
  imageId: string;
  userId: string;
  imageBuffer?: Buffer;
  metadata?: any;
  mode: string;
  settings?: any;
}

interface LayoutAnalysisJobData {
  imageId: string;
  userId: string;
  components: any[];
  imageMetadata: any;
  settings?: any;
}

interface DesignTokenExtractionJobData {
  imageId: string;
  userId: string;
  components: any[];
  layoutData: any;
  imageBuffer?: Buffer;
  settings?: any;
}

interface QualityAssessmentJobData {
  imageId: string;
  userId: string;
  components: any[];
  layoutData: any;
  designTokens: any;
  settings?: any;
}

interface ResultAggregationJobData {
  imageId: string;
  userId: string;
  preprocessingResults: any;
  componentResults: any;
  layoutResults: any;
  tokenResults: any;
  qualityResults: any;
  settings?: any;
}

interface EmailJobData {
  to: string;
  subject: string;
  template: string;
  data: any;
}

interface TokenDeductionJobData {
  userId: string;
  amount: number;
  description: string;
  resourceId?: string;
}

class QueueServiceClass {
  private imageProcessingQueue: Bull.Queue<ImageProcessingJobData> | null = null;
  private emailQueue: Bull.Queue<EmailJobData> | null = null;
  private tokenQueue: Bull.Queue<TokenDeductionJobData> | null = null;

  // Multi-stage processing queues
  private imagePreprocessingQueue: Bull.Queue<ImagePreprocessingJobData> | null = null;
  private componentDetectionQueue: Bull.Queue<ComponentDetectionJobData> | null = null;
  private layoutAnalysisQueue: Bull.Queue<LayoutAnalysisJobData> | null = null;
  private designTokenExtractionQueue: Bull.Queue<DesignTokenExtractionJobData> | null = null;
  private qualityAssessmentQueue: Bull.Queue<QualityAssessmentJobData> | null = null;
  private resultAggregationQueue: Bull.Queue<ResultAggregationJobData> | null = null;

  private isInitialized: boolean = false;

  // Getter methods to expose queues for WebSocket service
  getImageProcessingQueue(): Bull.Queue<ImageProcessingJobData> | null {
    return this.imageProcessingQueue;
  }

  getEmailQueue(): Bull.Queue<EmailJobData> | null {
    return this.emailQueue;
  }

  getTokenQueue(): Bull.Queue<TokenDeductionJobData> | null {
    return this.tokenQueue;
  }

  // Multi-stage processing queue getters
  getImagePreprocessingQueue(): Bull.Queue<ImagePreprocessingJobData> | null {
    return this.imagePreprocessingQueue;
  }

  getComponentDetectionQueue(): Bull.Queue<ComponentDetectionJobData> | null {
    return this.componentDetectionQueue;
  }

  getLayoutAnalysisQueue(): Bull.Queue<LayoutAnalysisJobData> | null {
    return this.layoutAnalysisQueue;
  }

  getDesignTokenExtractionQueue(): Bull.Queue<DesignTokenExtractionJobData> | null {
    return this.designTokenExtractionQueue;
  }

  getQualityAssessmentQueue(): Bull.Queue<QualityAssessmentJobData> | null {
    return this.qualityAssessmentQueue;
  }

  getResultAggregationQueue(): Bull.Queue<ResultAggregationJobData> | null {
    return this.resultAggregationQueue;
  }

  async initialize(mode: 'producer' | 'consumer' = 'producer'): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';

      console.log(`🔧 Initializing Queue service in ${mode} mode...`);

      // Initialize queues
      this.imageProcessingQueue = new Bull('image-processing', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      this.emailQueue = new Bull('email', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 50,
          removeOnFail: 10,
          attempts: 5,
          backoff: {
            type: 'exponential',
            delay: 1000,
          },
        },
      });

      this.tokenQueue = new Bull('token-deduction', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 20,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 500,
          },
        },
      });

      // Initialize multi-stage processing queues
      this.imagePreprocessingQueue = new Bull('image-preprocessing', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
        },
      });

      this.componentDetectionQueue = new Bull('component-detection', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: { type: 'exponential', delay: 2000 },
        },
      });

      this.layoutAnalysisQueue = new Bull('layout-analysis', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 2,
          backoff: { type: 'exponential', delay: 1000 },
        },
      });

      this.designTokenExtractionQueue = new Bull('design-token-extraction', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 2,
          backoff: { type: 'exponential', delay: 1000 },
        },
      });

      this.qualityAssessmentQueue = new Bull('quality-assessment', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 2,
          backoff: { type: 'exponential', delay: 1000 },
        },
      });

      this.resultAggregationQueue = new Bull('result-aggregation', redisUrl, {
        defaultJobOptions: {
          removeOnComplete: 10,
          removeOnFail: 5,
          attempts: 3,
          backoff: { type: 'exponential', delay: 1000 },
        },
      });

      // Only set up job processors in consumer mode (worker process)
      if (mode === 'consumer') {
        console.log('🔄 Setting up job processors for worker...');
        this.setupJobProcessors();
      } else {
        console.log('📤 Producer mode - job processors not initialized (jobs will be created only)');
      }

      this.isInitialized = true;
      console.log(`✓ Queue service initialized successfully in ${mode} mode`);

    } catch (error) {
      console.error('Failed to initialize queue service:', error);
      // Don't throw error in development
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  private setupJobProcessors(): void {
    // Legacy image processing job processor (for backward compatibility)
    this.imageProcessingQueue?.process(async (job) => {
      try {
        console.log(`🔄 Job processor started for job ${job.id}`);
        console.log(`📋 Job data:`, JSON.stringify(job.data, null, 2));

        const { imageId, userId, s3Key, mode, settings } = job.data;

        console.log(`🖼️ Processing image ${imageId} for user ${userId}`);

        try {
          // Update job progress
          console.log(`📊 Setting job progress to 10%`);
          await job.progress(10);
          await this.updateProcessingJobProgress(imageId, 10);

          // Process image with Sharp.js
          console.log(`🔧 Starting Sharp.js processing...`);
          const processingResults = await this.processImageWithSharp(job);
          console.log(`✅ Sharp.js processing completed:`, processingResults);

          // Update database with processing results
          console.log(`💾 Updating database with results...`);
          await this.updateImageProcessingResults(imageId, {
            status: 'COMPLETED',
            processedAt: new Date(),
            results: {
              mode,
              settings,
              ...processingResults
            }
          });

          // Update processing job as completed
          await this.updateProcessingJobCompleted(imageId, processingResults);

          console.log(`🎉 Image processing completed for ${imageId}`);
          return { success: true, imageId };

        } catch (error) {
          console.error(`❌ Image processing failed for ${imageId}:`, error);

        // Update database with error status
        await this.updateImageProcessingResults(imageId, {
          status: 'FAILED',
          error: error instanceof Error ? error.message : 'Unknown error'
        });

        // Update processing job as failed
        await this.updateProcessingJobFailed(imageId, error instanceof Error ? error.message : 'Unknown error');

        throw error;
        }
      } catch (outerError) {
        console.error(`🚨 CRITICAL: Job processor crashed for job ${job.id}:`, outerError);
        throw outerError;
      }
    });

    // Email job processor
    this.emailQueue?.process(async (job) => {
      const { to, subject, template, data } = job.data;
      
      console.log(`Sending email to ${to}: ${subject}`);
      
      try {
        // Simulate email sending (replace with actual email service)
        await this.simulateEmailSending(job);
        
        console.log(`Email sent successfully to ${to}`);
        return { success: true, to };

      } catch (error) {
        console.error(`Email sending failed to ${to}:`, error);
        throw error;
      }
    });

    // Token deduction job processor
    this.tokenQueue?.process(async (job) => {
      const { userId, amount, description, resourceId } = job.data;
      
      console.log(`Processing token deduction for user ${userId}: ${amount} tokens`);
      
      try {
        // Deduct tokens from user account
        await this.processTokenDeduction(userId, amount, description, resourceId);
        
        console.log(`Token deduction completed for user ${userId}`);
        return { success: true, userId, amount };

      } catch (error) {
        console.error(`Token deduction failed for user ${userId}:`, error);
        throw error;
      }
    });

    // Multi-stage processing job processors
    this.setupMultiStageProcessors();
  }

  private setupMultiStageProcessors(): void {
    // Stage 1: Image Preprocessing
    this.imagePreprocessingQueue?.process(async (job) => {
      console.log(`🔄 [Stage 1] Image Preprocessing started for job ${job.id}`);
      const result = await this.processImagePreprocessing(job);

      // Create next stage job
      await this.addComponentDetectionJob({
        imageId: job.data.imageId,
        userId: job.data.userId,
        imageBuffer: result.imageBuffer,
        metadata: result.metadata,
        mode: job.data.mode,
        settings: job.data.settings
      });

      return result;
    });

    // Stage 2: Component Detection
    this.componentDetectionQueue?.process(async (job) => {
      console.log(`🔄 [Stage 2] Component Detection started for job ${job.id}`);
      const result = await this.processComponentDetection(job);

      // Create next stage job
      await this.addLayoutAnalysisJob({
        imageId: job.data.imageId,
        userId: job.data.userId,
        components: result.components,
        imageMetadata: job.data.metadata,
        settings: job.data.settings
      });

      return result;
    });

    // Stage 3: Layout Analysis
    this.layoutAnalysisQueue?.process(async (job) => {
      console.log(`🔄 [Stage 3] Layout Analysis started for job ${job.id}`);
      const result = await this.processLayoutAnalysis(job);

      // Create next stage job
      await this.addDesignTokenExtractionJob({
        imageId: job.data.imageId,
        userId: job.data.userId,
        components: job.data.components,
        layoutData: result.layoutData,
        settings: job.data.settings
      });

      return result;
    });

    // Stage 4: Design Token Extraction
    this.designTokenExtractionQueue?.process(async (job) => {
      console.log(`🔄 [Stage 4] Design Token Extraction started for job ${job.id}`);
      const result = await this.processDesignTokenExtraction(job);

      // Create next stage job
      await this.addQualityAssessmentJob({
        imageId: job.data.imageId,
        userId: job.data.userId,
        components: job.data.components,
        layoutData: job.data.layoutData,
        designTokens: result.designTokens,
        settings: job.data.settings
      });

      return result;
    });

    // Stage 5: Quality Assessment
    this.qualityAssessmentQueue?.process(async (job) => {
      console.log(`🔄 [Stage 5] Quality Assessment started for job ${job.id}`);
      const result = await this.processQualityAssessment(job);

      // Create final aggregation job
      await this.addResultAggregationJob({
        imageId: job.data.imageId,
        userId: job.data.userId,
        preprocessingResults: {}, // Will be retrieved from cache
        componentResults: job.data.components,
        layoutResults: job.data.layoutData,
        tokenResults: job.data.designTokens,
        qualityResults: result.qualityMetrics,
        settings: job.data.settings
      });

      return result;
    });

    // Stage 6: Result Aggregation
    this.resultAggregationQueue?.process(async (job) => {
      console.log(`🔄 [Stage 6] Result Aggregation started for job ${job.id}`);
      const result = await this.processResultAggregation(job);

      // Update final image status
      await this.updateImageProcessingResults(job.data.imageId, {
        status: 'COMPLETED',
        processedAt: new Date(),
        results: result
      });

      console.log(`✅ Multi-stage processing completed for image ${job.data.imageId}`);
      return result;
    });
  }

  // Job creation methods
  async addImageProcessingJob(data: ImageProcessingJobData, options?: Bull.JobOptions): Promise<Bull.Job<ImageProcessingJobData> | null> {
    try {
      if (!this.imageProcessingQueue) {
        console.warn('Image processing queue not available');
        return null;
      }

      const job = await this.imageProcessingQueue.add('process-image', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Image processing job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add image processing job:', error);
      return null;
    }
  }

  async addEmailJob(data: EmailJobData, options?: Bull.JobOptions): Promise<Bull.Job<EmailJobData> | null> {
    try {
      if (!this.emailQueue) {
        console.warn('Email queue not available');
        return null;
      }

      const job = await this.emailQueue.add('send-email', data, {
        priority: 2,
        ...options,
      });

      console.log(`Email job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add email job:', error);
      return null;
    }
  }

  async addTokenDeductionJob(data: TokenDeductionJobData, options?: Bull.JobOptions): Promise<Bull.Job<TokenDeductionJobData> | null> {
    try {
      if (!this.tokenQueue) {
        console.warn('Token queue not available');
        return null;
      }

      const job = await this.tokenQueue.add('deduct-tokens', data, {
        priority: 3,
        ...options,
      });

      console.log(`Token deduction job created: ${job.id}`);
      return job;

    } catch (error) {
      console.error('Failed to add token deduction job:', error);
      return null;
    }
  }

  // Multi-stage processing job creation methods
  async addImagePreprocessingJob(data: ImagePreprocessingJobData, options?: Bull.JobOptions): Promise<Bull.Job<ImagePreprocessingJobData> | null> {
    try {
      if (!this.imagePreprocessingQueue) {
        console.warn('Image preprocessing queue not available');
        return null;
      }

      const job = await this.imagePreprocessingQueue.add('preprocess-image', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Image preprocessing job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add image preprocessing job:', error);
      return null;
    }
  }

  async addComponentDetectionJob(data: ComponentDetectionJobData, options?: Bull.JobOptions): Promise<Bull.Job<ComponentDetectionJobData> | null> {
    try {
      if (!this.componentDetectionQueue) {
        console.warn('Component detection queue not available');
        return null;
      }

      const job = await this.componentDetectionQueue.add('detect-components', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Component detection job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add component detection job:', error);
      return null;
    }
  }

  async addLayoutAnalysisJob(data: LayoutAnalysisJobData, options?: Bull.JobOptions): Promise<Bull.Job<LayoutAnalysisJobData> | null> {
    try {
      if (!this.layoutAnalysisQueue) {
        console.warn('Layout analysis queue not available');
        return null;
      }

      const job = await this.layoutAnalysisQueue.add('analyze-layout', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Layout analysis job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add layout analysis job:', error);
      return null;
    }
  }

  async addDesignTokenExtractionJob(data: DesignTokenExtractionJobData, options?: Bull.JobOptions): Promise<Bull.Job<DesignTokenExtractionJobData> | null> {
    try {
      if (!this.designTokenExtractionQueue) {
        console.warn('Design token extraction queue not available');
        return null;
      }

      const job = await this.designTokenExtractionQueue.add('extract-tokens', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Design token extraction job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add design token extraction job:', error);
      return null;
    }
  }

  async addQualityAssessmentJob(data: QualityAssessmentJobData, options?: Bull.JobOptions): Promise<Bull.Job<QualityAssessmentJobData> | null> {
    try {
      if (!this.qualityAssessmentQueue) {
        console.warn('Quality assessment queue not available');
        return null;
      }

      const job = await this.qualityAssessmentQueue.add('assess-quality', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Quality assessment job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add quality assessment job:', error);
      return null;
    }
  }

  async addResultAggregationJob(data: ResultAggregationJobData, options?: Bull.JobOptions): Promise<Bull.Job<ResultAggregationJobData> | null> {
    try {
      if (!this.resultAggregationQueue) {
        console.warn('Result aggregation queue not available');
        return null;
      }

      const job = await this.resultAggregationQueue.add('aggregate-results', data, {
        priority: 1,
        delay: 0,
        ...options,
      });

      console.log(`Result aggregation job created: ${job.id}`);
      return job;
    } catch (error) {
      console.error('Failed to add result aggregation job:', error);
      return null;
    }
  }

  // Queue status methods
  async getQueueStats() {
    try {
      const [imageStats, emailStats, tokenStats] = await Promise.all([
        this.imageProcessingQueue?.getJobCounts(),
        this.emailQueue?.getJobCounts(),
        this.tokenQueue?.getJobCounts(),
      ]);

      return {
        imageProcessing: imageStats || {},
        email: emailStats || {},
        tokenDeduction: tokenStats || {},
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return {};
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false;
      }

      // Check if queues are responsive
      const stats = await this.getQueueStats();
      return Object.keys(stats).length > 0;
    } catch (error) {
      console.error('Queue health check failed:', error);
      return false;
    }
  }

  // Helper methods with actual implementation
  private async simulateImageProcessing(job: Bull.Job): Promise<void> {
    const { imageId, mode, settings } = job.data;

    // Step 1: Initialize processing
    await job.progress(10);
    console.log(`Starting ${mode} processing for image ${imageId}`);

    // Step 2: Load and validate image
    await job.progress(25);
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`Image loaded and validated: ${imageId}`);

    // Step 3: Apply processing based on mode
    await job.progress(50);
    await new Promise(resolve => setTimeout(resolve, 1000));

    switch (mode) {
      case 'sketch':
        console.log(`Applying sketch conversion to ${imageId}`);
        break;
      case 'cartoon':
        console.log(`Applying cartoon conversion to ${imageId}`);
        break;
      case 'anime':
        console.log(`Applying anime conversion to ${imageId}`);
        break;
      default:
        console.log(`Applying default processing to ${imageId}`);
    }

    // Step 4: Post-processing and optimization
    await job.progress(75);
    await new Promise(resolve => setTimeout(resolve, 500));
    console.log(`Post-processing completed for ${imageId}`);

    // Step 5: Save results
    await job.progress(90);
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log(`Results saved for ${imageId}`);

    await job.progress(100);
  }

  private async simulateEmailSending(job: Bull.Job): Promise<void> {
    const { to, template } = job.data;

    // Simulate email template processing
    await new Promise(resolve => setTimeout(resolve, 200));
    console.log(`Processing ${template} template for ${to}`);

    // Simulate email delivery
    await new Promise(resolve => setTimeout(resolve, 300));
    console.log(`Email delivered to ${to}`);
  }

  private async processImageWithSharp(job: Bull.Job): Promise<any> {
    const { imageId, s3Key, s3Bucket, mode, userId } = job.data;

    try {
      console.log(`Starting AI-powered image processing for image ${imageId}`);
      await job.progress(10);

      // Download image from S3 (or get from local storage in mock mode)
      console.log(`Downloading image from S3: ${s3Key}`);
      const imageBuffer = await S3Service.getObject(s3Key, s3Bucket);
      await job.progress(20);

      // Extract metadata using Sharp
      console.log(`Extracting metadata for image ${imageId}`);
      const metadata = await sharp(imageBuffer).metadata();
      await job.progress(30);

      // Generate thumbnail
      console.log(`Generating thumbnail for image ${imageId}`);
      const thumbnailBuffer = await sharp(imageBuffer)
        .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 80 })
        .toBuffer();
      await job.progress(40);

      // Basic image analysis with Sharp
      console.log(`Analyzing image statistics for ${imageId}`);
      const stats = await sharp(imageBuffer).stats();
      await job.progress(50);

      // AI-powered component detection using OpenAI Vision
      console.log(`🤖 Starting AI component detection for ${imageId} (mode: ${mode})`);

      const processingOptions: ProcessingOptions = {
        mode: mode as any || 'auto',
        includeHierarchy: true,
        minConfidence: 0.7,
        maxComponents: 50
      };

      const aiAnalysis: VisionAnalysisResult = await OpenAIService.analyzeImage(
        imageBuffer,
        processingOptions,
        userId
      );

      // Update metadata with actual image dimensions
      aiAnalysis.metadata.imageWidth = metadata.width || 0;
      aiAnalysis.metadata.imageHeight = metadata.height || 0;

      await job.progress(90);

      console.log(`✅ AI analysis completed for ${imageId}: ${aiAnalysis.components.length} components detected`);
      await job.progress(100);

      console.log(`Image processing completed for ${imageId}`);

      return {
        metadata: {
          width: metadata.width,
          height: metadata.height,
          format: metadata.format,
          colorSpace: metadata.space,
          channels: metadata.channels,
          density: metadata.density
        },
        stats: {
          channels: stats.channels.map(ch => ({
            min: ch.min,
            max: ch.max,
            sum: ch.sum,
            mean: Math.round(ch.mean * 100) / 100
          }))
        },
        thumbnailGenerated: true,
        thumbnailSize: thumbnailBuffer.length,
        // AI Analysis Results
        aiAnalysis: {
          components: aiAnalysis.components,
          hierarchy: aiAnalysis.hierarchy,
          processingTime: aiAnalysis.metadata.processingTime,
          modelUsed: aiAnalysis.metadata.modelUsed,
          confidence: aiAnalysis.metadata.confidence,
          componentCount: aiAnalysis.components.length
        },
        mode: mode,
        processedAt: new Date().toISOString()
      };

    } catch (error) {
      console.error(`Sharp processing failed for image ${imageId}:`, error);
      throw error;
    }
  }

  private async updateImageProcessingResults(imageId: string, results: any): Promise<void> {
    try {
      // Import DatabaseService dynamically to avoid circular dependencies
      const { DatabaseService } = await import('./database');

      // Update image status in database
      await DatabaseService.client.image.update({
        where: { id: imageId },
        data: {
          status: results.status,
          processedAt: results.processedAt,
          processingResults: results.results ? JSON.stringify(results.results) : null,
          error: results.error || null,
        }
      });

      console.log(`Database updated for image ${imageId}: ${results.status}`);
    } catch (error) {
      console.error(`Failed to update database for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processTokenDeduction(userId: string, amount: number, description: string, resourceId?: string): Promise<void> {
    try {
      // Import DatabaseService dynamically to avoid circular dependencies
      const { DatabaseService } = await import('./database');

      // Create token transaction
      await DatabaseService.client.tokenTransaction.create({
        data: {
          userId,
          amount: -Math.abs(amount), // Ensure negative for deduction
          type: 'DEDUCTION',
          description,
          resourceId,
        }
      });

      // Update user token balance
      await DatabaseService.client.user.update({
        where: { id: userId },
        data: {
          tokenBalance: {
            decrement: Math.abs(amount)
          }
        }
      });

      console.log(`Deducted ${amount} tokens from user ${userId}: ${description}`);
    } catch (error) {
      console.error(`Failed to process token deduction for user ${userId}:`, error);
      throw error;
    }
  }

  async disconnect(): Promise<void> {
    try {
      await Promise.all([
        this.imageProcessingQueue?.close(),
        this.emailQueue?.close(),
        this.tokenQueue?.close(),
      ]);
      
      this.isInitialized = false;
      console.log('Queue service disconnected');
    } catch (error) {
      console.error('Failed to disconnect queue service:', error);
    }
  }

  // Helper methods for ProcessingJob updates
  private async updateProcessingJobProgress(imageId: string, progress: number): Promise<void> {
    try {
      const { DatabaseService } = await import('./database');

      // Find the processing job for this image
      const jobs = await DatabaseService.getProcessingJobs({
        userId: '', // We'll need to get this from the image
        limit: 1
      });

      // Find the job by imageId (since we don't have direct lookup)
      const job = jobs.find(j => j.imageId === imageId && j.status === 'RUNNING');

      if (job) {
        await DatabaseService.updateProcessingJob(job.id, {
          progress: progress / 100, // Convert to decimal
        });
      }
    } catch (error) {
      console.error(`Failed to update processing job progress for image ${imageId}:`, error);
    }
  }

  private async updateProcessingJobCompleted(imageId: string, results: any): Promise<void> {
    try {
      const { DatabaseService } = await import('./database');

      // Find the processing job for this image
      const jobs = await DatabaseService.getProcessingJobs({
        userId: '', // We'll need to get this from the image
        limit: 1
      });

      const job = jobs.find(j => j.imageId === imageId && j.status === 'RUNNING');

      if (job) {
        await DatabaseService.updateProcessingJob(job.id, {
          status: 'COMPLETED',
          progress: 1.0,
          output: results,
          completedAt: new Date(),
          processingTime: job.startedAt ? Date.now() - job.startedAt.getTime() : 0,
        });
      }
    } catch (error) {
      console.error(`Failed to update processing job completion for image ${imageId}:`, error);
    }
  }

  private async updateProcessingJobFailed(imageId: string, errorMessage: string): Promise<void> {
    try {
      const { DatabaseService } = await import('./database');

      // Find the processing job for this image
      const jobs = await DatabaseService.getProcessingJobs({
        userId: '', // We'll need to get this from the image
        limit: 1
      });

      const job = jobs.find(j => j.imageId === imageId && (j.status === 'RUNNING' || j.status === 'PENDING'));

      if (job) {
        await DatabaseService.updateProcessingJob(job.id, {
          status: 'FAILED',
          error: errorMessage,
          completedAt: new Date(),
          processingTime: job.startedAt ? Date.now() - job.startedAt.getTime() : 0,
        });
      }
    } catch (error) {
      console.error(`Failed to update processing job failure for image ${imageId}:`, error);
    }
  }
  // Multi-stage processing methods
  private async processImagePreprocessing(job: Bull.Job<ImagePreprocessingJobData>): Promise<any> {
    const { imageId, s3Key, s3Bucket, mode } = job.data;

    try {
      console.log(`🔧 [Stage 1] Preprocessing image ${imageId}`);
      await job.progress(10);

      // Download image from S3
      const imageBuffer = await S3Service.getObject(s3Key, s3Bucket);
      await job.progress(30);

      // Extract metadata using Sharp
      const metadata = await sharp(imageBuffer).metadata();
      await job.progress(50);

      // Generate thumbnail
      const thumbnailBuffer = await sharp(imageBuffer)
        .resize(200, 200, { fit: 'inside', withoutEnlargement: true })
        .jpeg({ quality: 80 })
        .toBuffer();
      await job.progress(70);

      // Basic image analysis
      const stats = await sharp(imageBuffer).stats();
      await job.progress(90);

      const result = {
        imageBuffer,
        metadata: {
          ...metadata,
          stats,
          mode
        },
        thumbnailBuffer
      };

      // Cache preprocessing results
      await this.cacheStageResult(imageId, 'preprocessing', result);
      await job.progress(100);

      console.log(`✅ [Stage 1] Preprocessing completed for image ${imageId}`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 1] Preprocessing failed for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processComponentDetection(job: Bull.Job<ComponentDetectionJobData>): Promise<any> {
    const { imageId, userId, imageBuffer, metadata, mode } = job.data;

    try {
      console.log(`🤖 [Stage 2] Detecting components for image ${imageId}`);
      await job.progress(10);

      const processingOptions: ProcessingOptions = {
        mode: mode as any || 'auto',
        includeHierarchy: true,
        minConfidence: 0.7,
        maxComponents: 50
      };

      await job.progress(30);

      const aiAnalysis: VisionAnalysisResult = await OpenAIService.analyzeImage(
        imageBuffer!,
        processingOptions,
        userId
      );

      await job.progress(80);

      // Update metadata with actual image dimensions
      aiAnalysis.metadata.imageWidth = metadata?.width || 0;
      aiAnalysis.metadata.imageHeight = metadata?.height || 0;

      const result = {
        components: aiAnalysis.components,
        hierarchy: aiAnalysis.hierarchy,
        metadata: aiAnalysis.metadata
      };

      // Cache component detection results
      await this.cacheStageResult(imageId, 'component-detection', result);
      await RedisService.cacheProcessedComponents(imageId, aiAnalysis.components, aiAnalysis.hierarchy, 7200);
      await job.progress(100);

      console.log(`✅ [Stage 2] Component detection completed for image ${imageId} (${aiAnalysis.components.length} components)`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 2] Component detection failed for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processLayoutAnalysis(job: Bull.Job<LayoutAnalysisJobData>): Promise<any> {
    const { imageId, components, imageMetadata } = job.data;

    try {
      console.log(`📐 [Stage 3] Analyzing layout for image ${imageId}`);
      await job.progress(10);

      // Analyze spatial relationships between components
      const spatialRelationships = this.analyzeSpatialRelationships(components);
      await job.progress(40);

      // Detect layout patterns (grid, flex, etc.)
      const layoutPatterns = this.detectLayoutPatterns(components, imageMetadata);
      await job.progress(70);

      // Identify containers and groupings
      const containers = this.identifyContainers(components);
      await job.progress(90);

      const result = {
        layoutData: {
          spatialRelationships,
          layoutPatterns,
          containers,
          gridSystem: this.detectGridSystem(components),
          alignments: this.detectAlignments(components)
        }
      };

      // Cache layout analysis results
      await this.cacheStageResult(imageId, 'layout', result);
      await job.progress(100);

      console.log(`✅ [Stage 3] Layout analysis completed for image ${imageId}`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 3] Layout analysis failed for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processDesignTokenExtraction(job: Bull.Job<DesignTokenExtractionJobData>): Promise<any> {
    const { imageId, components, layoutData } = job.data;

    try {
      console.log(`🎨 [Stage 4] Extracting design tokens for image ${imageId}`);
      await job.progress(10);

      // Extract color palette
      const colorTokens = this.extractColorTokens(components);
      await job.progress(30);

      // Analyze typography
      const typographyTokens = this.extractTypographyTokens(components);
      await job.progress(50);

      // Extract spacing patterns
      const spacingTokens = this.extractSpacingTokens(components, layoutData);
      await job.progress(70);

      // Identify component variants
      const componentVariants = this.identifyComponentVariants(components);
      await job.progress(90);

      const result = {
        designTokens: {
          colors: colorTokens,
          typography: typographyTokens,
          spacing: spacingTokens,
          components: componentVariants,
          breakpoints: this.detectBreakpoints(layoutData)
        }
      };

      // Cache design token results
      await this.cacheStageResult(imageId, 'design-tokens', result);
      await RedisService.cacheDesignTokens(imageId, result.designTokens, 7200);
      await job.progress(100);

      console.log(`✅ [Stage 4] Design token extraction completed for image ${imageId}`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 4] Design token extraction failed for image ${imageId}:`, error);
      throw error;
    }
  }

  // Helper methods for stage processing
  private async cacheStageResult(imageId: string, stage: string, result: any): Promise<void> {
    // Cache results in Redis for fast access between stages
    try {
      await RedisService.cacheStageResult(imageId, stage, result, 3600); // 1 hour TTL
      console.log(`📦 Successfully cached ${stage} results for image ${imageId}`);
    } catch (error) {
      console.error(`Failed to cache ${stage} results for image ${imageId}:`, error);
    }
  }

  private async getCachedStageResult(imageId: string, stage: string): Promise<any | null> {
    try {
      return await RedisService.getStageResult(imageId, stage);
    } catch (error) {
      console.error(`Failed to retrieve cached ${stage} results for image ${imageId}:`, error);
      return null;
    }
  }

  private analyzeSpatialRelationships(components: any[]): any {
    // Placeholder implementation
    return { relationships: [] };
  }

  private detectLayoutPatterns(components: any[], metadata: any): any {
    // Placeholder implementation
    return { patterns: [] };
  }

  private identifyContainers(components: any[]): any {
    // Placeholder implementation
    return { containers: [] };
  }

  private detectGridSystem(components: any[]): any {
    // Placeholder implementation
    return { grid: null };
  }

  private detectAlignments(components: any[]): any {
    // Placeholder implementation
    return { alignments: [] };
  }

  private extractColorTokens(components: any[]): any {
    // Placeholder implementation
    return { colors: [] };
  }

  private extractTypographyTokens(components: any[]): any {
    // Placeholder implementation
    return { typography: [] };
  }

  private extractSpacingTokens(components: any[], layoutData: any): any {
    // Placeholder implementation
    return { spacing: [] };
  }

  private identifyComponentVariants(components: any[]): any {
    // Placeholder implementation
    return { variants: [] };
  }

  private detectBreakpoints(layoutData: any): any {
    // Placeholder implementation
    return { breakpoints: [] };
  }

  private async processQualityAssessment(job: Bull.Job<QualityAssessmentJobData>): Promise<any> {
    const { imageId, components, layoutData, designTokens } = job.data;

    try {
      console.log(`🔍 [Stage 5] Assessing quality for image ${imageId}`);
      await job.progress(10);

      // Accessibility assessment
      const accessibilityScore = this.assessAccessibility(components, designTokens);
      await job.progress(30);

      // Design consistency check
      const consistencyScore = this.assessDesignConsistency(components, designTokens);
      await job.progress(50);

      // Performance impact analysis
      const performanceScore = this.assessPerformanceImpact(components, layoutData);
      await job.progress(70);

      // Best practices validation
      const bestPracticesScore = this.assessBestPractices(components, layoutData);
      await job.progress(90);

      const result = {
        qualityMetrics: {
          accessibility: accessibilityScore,
          consistency: consistencyScore,
          performance: performanceScore,
          bestPractices: bestPracticesScore,
          overallScore: this.calculateOverallScore([
            accessibilityScore,
            consistencyScore,
            performanceScore,
            bestPracticesScore
          ])
        }
      };

      // Cache quality assessment results
      await this.cacheStageResult(imageId, 'quality', result);
      await job.progress(100);

      console.log(`✅ [Stage 5] Quality assessment completed for image ${imageId}`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 5] Quality assessment failed for image ${imageId}:`, error);
      throw error;
    }
  }

  private async processResultAggregation(job: Bull.Job<ResultAggregationJobData>): Promise<any> {
    const { imageId, componentResults, layoutResults, tokenResults, qualityResults } = job.data;

    try {
      console.log(`🔗 [Stage 6] Aggregating results for image ${imageId}`);
      await job.progress(10);

      // Combine all expert results
      const aggregatedResults = {
        components: componentResults,
        layout: layoutResults,
        designTokens: tokenResults,
        quality: qualityResults,
        metadata: {
          processedAt: new Date(),
          processingVersion: '2.0',
          stages: ['preprocessing', 'components', 'layout', 'tokens', 'quality', 'aggregation']
        }
      };

      await job.progress(50);

      // Generate confidence scores and resolve conflicts
      const finalResults = this.resolveExpertConflicts(aggregatedResults);
      await job.progress(80);

      // Generate comprehensive analysis report
      const analysisReport = this.generateAnalysisReport(finalResults);
      await job.progress(95);

      const result = {
        ...finalResults,
        analysisReport,
        processingStats: {
          totalComponents: componentResults?.length || 0,
          layoutPatterns: layoutResults?.layoutData?.patterns?.length || 0,
          designTokens: Object.keys(tokenResults?.designTokens || {}).length,
          qualityScore: qualityResults?.qualityMetrics?.overallScore || 0
        }
      };

      // Store all results in database for persistence
      await AnalysisStorageService.batchStoreResults(imageId, job.data.userId || '', {
        componentDetection: {
          components: componentResults?.components || [],
          hierarchy: componentResults?.hierarchy || {},
          boundingBoxes: componentResults?.boundingBoxes || [],
          confidence: componentResults?.confidence || 0.8,
          processingTime: componentResults?.processingTime || 0
        },
        designTokens: {
          colors: tokenResults?.designTokens?.colors || {},
          typography: tokenResults?.designTokens?.typography || {},
          spacing: tokenResults?.designTokens?.spacing || {},
          components: tokenResults?.designTokens?.components || {},
          breakpoints: tokenResults?.designTokens?.breakpoints || {},
          confidence: tokenResults?.confidence || 0.8,
          tokenCount: Object.keys(tokenResults?.designTokens || {}).length
        },
        layoutAnalysis: layoutResults,
        qualityAssessment: qualityResults
      });

      await job.progress(100);

      console.log(`✅ [Stage 6] Result aggregation completed for image ${imageId} - stored in database`);
      return result;

    } catch (error) {
      console.error(`❌ [Stage 6] Result aggregation failed for image ${imageId}:`, error);
      throw error;
    }
  }

  // Quality assessment helper methods
  private assessAccessibility(components: any[], designTokens: any): number {
    // Placeholder implementation for accessibility scoring
    return 85;
  }

  private assessDesignConsistency(components: any[], designTokens: any): number {
    // Placeholder implementation for consistency scoring
    return 78;
  }

  private assessPerformanceImpact(components: any[], layoutData: any): number {
    // Placeholder implementation for performance scoring
    return 92;
  }

  private assessBestPractices(components: any[], layoutData: any): number {
    // Placeholder implementation for best practices scoring
    return 88;
  }

  private calculateOverallScore(scores: number[]): number {
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  }

  private resolveExpertConflicts(results: any): any {
    // Placeholder implementation for conflict resolution
    return results;
  }

  private generateAnalysisReport(results: any): any {
    // Placeholder implementation for report generation
    return {
      summary: 'Multi-stage AI analysis completed successfully',
      recommendations: [],
      insights: []
    };
  }

  // Method to start multi-stage processing
  async startMultiStageProcessing(data: ImagePreprocessingJobData, options?: Bull.JobOptions): Promise<Bull.Job<ImagePreprocessingJobData> | null> {
    console.log(`🚀 Starting multi-stage processing for image ${data.imageId}`);

    // Update image status to processing
    await this.updateImageProcessingResults(data.imageId, {
      status: 'PROCESSING',
      processedAt: new Date(),
      processingMode: 'multi-stage'
    });

    // Start with preprocessing stage
    return await this.addImagePreprocessingJob(data, options);
  }
}

export const QueueService = new QueueServiceClass();
export type {
  ImageProcessingJobData,
  EmailJobData,
  TokenDeductionJobData,
  ImagePreprocessingJobData,
  ComponentDetectionJobData,
  LayoutAnalysisJobData,
  DesignTokenExtractionJobData,
  QualityAssessmentJobData,
  ResultAggregationJobData
};
