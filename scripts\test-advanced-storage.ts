#!/usr/bin/env tsx

import { RedisService } from '../src/server/services/redis';
import { DatabaseService } from '../src/server/services/database';
import { AnalysisStorageService } from '../src/server/services/analysis-storage';
import { S3Service } from '../src/server/services/s3';

async function testAdvancedStorage() {
  console.log('🧪 Testing Advanced Result Storage and Caching System...\n');

  try {
    // Initialize services
    console.log('📋 Initializing services...');
    await RedisService.initialize();
    await DatabaseService.initialize();
    await S3Service.initialize();
    console.log('✅ Services initialized\n');

    // Test Redis caching enhancements
    console.log('🔄 Testing Redis caching enhancements...');
    
    const testImageId = 'test-image-123';
    const testStage = 'component-detection';
    const testResult = {
      components: [
        { id: 1, type: 'button', confidence: 0.95 },
        { id: 2, type: 'input', confidence: 0.88 }
      ],
      metadata: { processingTime: 1500 }
    };

    // Test stage result caching
    console.log('  📦 Testing stage result caching...');
    const cached = await RedisService.cacheStageResult(testImageId, testStage, testResult, 3600);
    console.log(`     Cache operation: ${cached ? '✅ Success' : '❌ Failed'}`);

    const retrieved = await RedisService.getStageResult(testImageId, testStage);
    console.log(`     Retrieval: ${retrieved ? '✅ Success' : '❌ Failed'}`);
    console.log(`     Data integrity: ${JSON.stringify(retrieved?.result) === JSON.stringify(testResult) ? '✅ Verified' : '❌ Failed'}`);

    // Test component caching
    console.log('  🔍 Testing component caching...');
    const components = [
      { id: 1, type: 'button', x: 10, y: 20, width: 100, height: 40 },
      { id: 2, type: 'input', x: 10, y: 80, width: 200, height: 30 }
    ];
    const hierarchy = { root: { children: [1, 2] } };

    const componentsCached = await RedisService.cacheProcessedComponents(testImageId, components, hierarchy, 7200);
    console.log(`     Component cache: ${componentsCached ? '✅ Success' : '❌ Failed'}`);

    const componentsRetrieved = await RedisService.getProcessedComponents(testImageId);
    console.log(`     Component retrieval: ${componentsRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Test design tokens caching
    console.log('  🎨 Testing design tokens caching...');
    const designTokens = {
      colors: { primary: '#007bff', secondary: '#6c757d' },
      typography: { fontFamily: 'Arial', fontSize: '16px' },
      spacing: { small: '8px', medium: '16px' },
      components: { button: { padding: '8px 16px' } },
      breakpoints: { mobile: '768px', desktop: '1024px' }
    };

    const tokensCached = await RedisService.cacheDesignTokens(testImageId, designTokens, 7200);
    console.log(`     Tokens cache: ${tokensCached ? '✅ Success' : '❌ Failed'}`);

    const tokensRetrieved = await RedisService.getDesignTokens(testImageId);
    console.log(`     Tokens retrieval: ${tokensRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Test pipeline status caching
    console.log('  ⚙️ Testing pipeline status caching...');
    const pipelineStatus = {
      currentStage: 3,
      totalStages: 6,
      progress: 50,
      status: 'RUNNING',
      startedAt: new Date().toISOString()
    };

    const pipelineCached = await RedisService.cachePipelineStatus(testImageId, pipelineStatus, 1800);
    console.log(`     Pipeline cache: ${pipelineCached ? '✅ Success' : '❌ Failed'}`);

    const pipelineRetrieved = await RedisService.getPipelineStatus(testImageId);
    console.log(`     Pipeline retrieval: ${pipelineRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Test batch operations
    console.log('  📦 Testing batch cache operations...');
    const operations = [
      { imageId: 'img1', stage: 'preprocessing', result: { status: 'complete' } },
      { imageId: 'img2', stage: 'component-detection', result: { components: [] } },
      { imageId: 'img3', stage: 'layout-analysis', result: { layout: {} } }
    ];

    const batchSuccess = await RedisService.batchCacheStageResults(operations);
    console.log(`     Batch operations: ${batchSuccess ? '✅ Success' : '❌ Failed'}`);

    // Test cache statistics
    console.log('  📊 Testing cache statistics...');
    const stats = await RedisService.getCacheStats();
    console.log(`     Cache stats: ${stats ? '✅ Success' : '❌ Failed'}`);
    if (stats) {
      console.log(`     Total cached items: ${stats.counts.total}`);
    }

    console.log('✅ Redis caching tests completed\n');

    // Test Database Schema and AnalysisStorageService
    console.log('💾 Testing Database Schema and Analysis Storage...');

    // Create test user and image first
    console.log('  👤 Creating test user and image...');
    const uniqueEmail = `test-${Date.now()}@example.com`;
    const testUser = await DatabaseService.createUser({
      email: uniqueEmail,
      name: 'Test User'
    });
    const testUserId = testUser.id;

    const testImage = await DatabaseService.createImage({
      userId: testUserId,
      filename: 'test-image.jpg',
      originalFilename: 'test-image.jpg',
      mimeType: 'image/jpeg',
      size: 1024,
      width: 800,
      height: 600,
      s3Key: 'test/test-image.jpg',
      s3Bucket: 'test-bucket'
    });
    const testImageId2 = testImage.id;
    console.log(`     User and image created: ✅ Success`);

    // Test analysis result storage
    console.log('  📊 Testing analysis result storage...');
    const analysisData = {
      imageId: testImageId2,
      userId: testUserId,
      analysisType: 'COMPONENT_DETECTION',
      processingMode: 'MULTI_STAGE',
      results: { components: [], confidence: 0.9 },
      confidence: 0.9,
      processingTime: 2500,
      componentCount: 5,
      qualityScore: 85
    };

    const resultId = await AnalysisStorageService.storeAnalysisResult(analysisData);
    console.log(`     Analysis storage: ${resultId ? '✅ Success' : '❌ Failed'}`);

    const analysisRetrieved = await AnalysisStorageService.getAnalysisResult(testImageId2, 'COMPONENT_DETECTION');
    console.log(`     Analysis retrieval: ${analysisRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Test component detection storage
    console.log('  🔍 Testing component detection storage...');
    const componentData = {
      imageId: testImageId2,
      userId: testUserId,
      components: [
        { id: 1, type: 'button', confidence: 0.95 },
        { id: 2, type: 'input', confidence: 0.88 }
      ],
      hierarchy: { root: { children: [1, 2] } },
      boundingBoxes: [
        { id: 1, x: 10, y: 20, width: 100, height: 40 },
        { id: 2, x: 10, y: 80, width: 200, height: 30 }
      ],
      confidence: 0.91,
      processingTime: 1800
    };

    const detectionId = await AnalysisStorageService.storeComponentDetection(componentData);
    console.log(`     Component storage: ${detectionId ? '✅ Success' : '❌ Failed'}`);

    const detectionRetrieved = await AnalysisStorageService.getComponentDetection(testImageId2);
    console.log(`     Component retrieval: ${detectionRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Test design tokens storage
    console.log('  🎨 Testing design tokens storage...');
    const tokenData = {
      imageId: testImageId2,
      userId: testUserId,
      colors: { primary: '#007bff', secondary: '#6c757d' },
      typography: { fontFamily: 'Arial', fontSize: '16px' },
      spacing: { small: '8px', medium: '16px' },
      components: { button: { padding: '8px 16px' } },
      breakpoints: { mobile: '768px', desktop: '1024px' },
      confidence: 0.87,
      tokenCount: 8
    };

    const tokenId = await AnalysisStorageService.storeDesignTokens(tokenData);
    console.log(`     Token storage: ${tokenId ? '✅ Success' : '❌ Failed'}`);

    const tokenRetrieved = await AnalysisStorageService.getDesignTokens(testImageId2);
    console.log(`     Token retrieval: ${tokenRetrieved ? '✅ Success' : '❌ Failed'}`);

    console.log('✅ Database storage tests completed\n');

    // Test S3 Storage for Generated Assets
    console.log('☁️ Testing S3 Storage for Generated Assets...');
    
    const testBuffer = Buffer.from('test file content for generated asset');
    const filename = 'test-asset.txt';
    const contentType = 'text/plain';

    const s3Result = await S3Service.uploadGeneratedAsset(testBuffer, filename, contentType, 'test-folder');
    console.log(`  📁 Asset upload: ${s3Result ? '✅ Success' : '❌ Failed'}`);
    if (s3Result) {
      console.log(`     Asset URL: ${s3Result.url}`);
    }

    // Test analysis report upload
    const reportData = {
      imageId: testImageId2,
      analysis: { components: [], tokens: {} },
      summary: 'Test analysis report',
      generatedAt: new Date().toISOString()
    };

    const reportResult = await S3Service.uploadAnalysisReport(reportData, testImageId2);
    console.log(`  📄 Report upload: ${reportResult ? '✅ Success' : '❌ Failed'}`);

    console.log('✅ S3 storage tests completed\n');

    // Test Integration (Cache-Database Fallback)
    console.log('🔄 Testing Integration (Cache-Database Fallback)...');

    // Create another test image for integration test
    const integrationImage = await DatabaseService.createImage({
      userId: testUserId,
      filename: 'integration-test.jpg',
      originalFilename: 'integration-test.jpg',
      mimeType: 'image/jpeg',
      size: 2048,
      width: 1024,
      height: 768,
      s3Key: 'test/integration-test.jpg',
      s3Bucket: 'test-bucket'
    });
    const integrationImageId = integrationImage.id;

    // Store in database only (bypass cache)
    await DatabaseService.createComponentDetection({
      imageId: integrationImageId,
      userId: testUserId,
      components: JSON.stringify([{ id: 1, type: 'button' }]),
      hierarchy: JSON.stringify({ root: { children: [1] } }),
      boundingBoxes: JSON.stringify([{ id: 1, x: 0, y: 0, width: 100, height: 40 }]),
      confidence: 0.9,
      processingTime: 1500
    });

    // Retrieve through AnalysisStorageService (should fallback to database and cache result)
    const integrationRetrieved = await AnalysisStorageService.getComponentDetection(integrationImageId);
    console.log(`  💾 Database fallback: ${integrationRetrieved ? '✅ Success' : '❌ Failed'}`);

    // Verify it was cached
    const integrationCached = await RedisService.getProcessedComponents(integrationImageId);
    console.log(`  📦 Auto-caching: ${integrationCached ? '✅ Success' : '❌ Failed'}`);

    console.log('✅ Integration tests completed\n');

    // Cleanup test data
    console.log('🧹 Cleaning up test data...');
    await RedisService.cleanupImageCache(testImageId);
    await RedisService.cleanupImageCache(testImageId2);
    await RedisService.cleanupImageCache(integrationImageId);
    await AnalysisStorageService.cleanupImageAnalysis(testImageId2);
    await AnalysisStorageService.cleanupImageAnalysis(integrationImageId);
    console.log('✅ Cleanup completed\n');

    console.log('🎉 All Advanced Result Storage and Caching tests passed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  } finally {
    // Disconnect services
    await RedisService.disconnect();
    await DatabaseService.disconnect();
  }
}

// Run the test
testAdvancedStorage().catch(console.error);
