// Jest setup file for Puppeteer tests

// Increase timeout for Puppeteer tests
jest.setTimeout(30000);

// Global test configuration
global.testConfig = {
  baseUrl: 'http://localhost:3002',
  headless: process.env.CI === 'true', // Run headless in CI
  slowMo: process.env.CI === 'true' ? 0 : 100,
  timeout: 30000
};

// Console log helper for debugging
global.logTest = (message) => {
  if (process.env.DEBUG_TESTS) {
    console.log(`[TEST] ${message}`);
  }
};
