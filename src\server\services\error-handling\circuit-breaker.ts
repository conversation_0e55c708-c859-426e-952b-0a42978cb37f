/**
 * Circuit Breaker Pattern Implementation
 * 
 * Prevents cascading failures by temporarily disabling failing services
 * and providing automatic recovery mechanisms.
 */

import { CircuitBreakerState, CircuitBreakerConfig, AppError, ErrorSeverity } from './types'

// Circuit breaker metrics
export interface CircuitBreakerMetrics {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  timeouts: number
  lastFailureTime?: Date
  lastSuccessTime?: Date
  state: CircuitBreakerState
  stateChangedAt: Date
  failureRate: number
}

// Circuit breaker events
export interface CircuitBreakerEvent {
  type: 'state_change' | 'request_success' | 'request_failure' | 'timeout'
  timestamp: Date
  state?: CircuitBreakerState
  previousState?: CircuitBreakerState
  error?: Error
  duration?: number
}

export class CircuitBreaker {
  private state: CircuitBreakerState = CircuitBreakerState.CLOSED
  private failureCount: number = 0
  private successCount: number = 0
  private lastFailureTime?: Date
  private lastSuccessTime?: Date
  private stateChangedAt: Date = new Date()
  private halfOpenCallCount: number = 0
  private metrics: CircuitBreakerMetrics
  private eventListeners: ((event: CircuitBreakerEvent) => void)[] = []

  private defaultConfig: CircuitBreakerConfig = {
    failureThreshold: 5,
    recoveryTimeout: 60000, // 1 minute
    monitoringPeriod: 300000, // 5 minutes
    halfOpenMaxCalls: 3,
    enabled: true
  }

  constructor(
    private name: string,
    private config: Partial<CircuitBreakerConfig> = {}
  ) {
    this.config = { ...this.defaultConfig, ...config }
    this.metrics = this.initializeMetrics()
    
    console.log(`🔧 Circuit breaker '${this.name}' initialized with config:`, this.config)
  }

  /**
   * Execute an operation through the circuit breaker
   */
  async execute<T>(operation: () => Promise<T>, timeout?: number): Promise<T> {
    if (!this.config.enabled) {
      return await operation()
    }

    // Check if circuit breaker should allow the request
    if (!this.canExecute()) {
      const error = new AppError(
        `Circuit breaker '${this.name}' is ${this.state}. Request rejected.`,
        'CIRCUIT_BREAKER_OPEN',
        'external_service' as any,
        ErrorSeverity.HIGH,
        false
      )
      this.emitEvent({ type: 'request_failure', timestamp: new Date(), error })
      throw error
    }

    const startTime = Date.now()
    
    try {
      // Execute with timeout if specified
      const result = timeout ? 
        await this.executeWithTimeout(operation, timeout) : 
        await operation()

      // Record success
      this.onSuccess(Date.now() - startTime)
      return result

    } catch (error) {
      // Record failure
      this.onFailure(error as Error, Date.now() - startTime)
      throw error
    }
  }

  /**
   * Execute operation with timeout
   */
  private async executeWithTimeout<T>(operation: () => Promise<T>, timeout: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timer = setTimeout(() => {
        const timeoutError = new AppError(
          `Operation timed out after ${timeout}ms`,
          'CIRCUIT_BREAKER_TIMEOUT',
          'timeout' as any,
          ErrorSeverity.HIGH,
          true
        )
        this.emitEvent({ 
          type: 'timeout', 
          timestamp: new Date(), 
          error: timeoutError,
          duration: timeout 
        })
        reject(timeoutError)
      }, timeout)

      operation()
        .then(result => {
          clearTimeout(timer)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timer)
          reject(error)
        })
    })
  }

  /**
   * Check if the circuit breaker allows execution
   */
  private canExecute(): boolean {
    switch (this.state) {
      case CircuitBreakerState.CLOSED:
        return true

      case CircuitBreakerState.OPEN:
        // Check if recovery timeout has passed
        if (this.shouldAttemptReset()) {
          this.transitionToHalfOpen()
          return true
        }
        return false

      case CircuitBreakerState.HALF_OPEN:
        // Allow limited number of calls in half-open state
        return this.halfOpenCallCount < this.config.halfOpenMaxCalls!

      default:
        return false
    }
  }

  /**
   * Handle successful operation
   */
  private onSuccess(duration: number): void {
    this.successCount++
    this.lastSuccessTime = new Date()
    this.metrics.successfulRequests++
    this.metrics.totalRequests++

    this.emitEvent({ 
      type: 'request_success', 
      timestamp: new Date(),
      duration 
    })

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      this.halfOpenCallCount++
      
      // If we've had enough successful calls, close the circuit
      if (this.halfOpenCallCount >= this.config.halfOpenMaxCalls!) {
        this.transitionToClosed()
      }
    }

    this.updateMetrics()
  }

  /**
   * Handle failed operation
   */
  private onFailure(error: Error, duration: number): void {
    this.failureCount++
    this.lastFailureTime = new Date()
    this.metrics.failedRequests++
    this.metrics.totalRequests++

    this.emitEvent({ 
      type: 'request_failure', 
      timestamp: new Date(),
      error,
      duration 
    })

    if (this.state === CircuitBreakerState.HALF_OPEN) {
      // Any failure in half-open state should open the circuit
      this.transitionToOpen()
    } else if (this.state === CircuitBreakerState.CLOSED) {
      // Check if we should open the circuit
      if (this.shouldOpen()) {
        this.transitionToOpen()
      }
    }

    this.updateMetrics()
  }

  /**
   * Check if circuit should open
   */
  private shouldOpen(): boolean {
    return this.failureCount >= this.config.failureThreshold!
  }

  /**
   * Check if circuit should attempt reset
   */
  private shouldAttemptReset(): boolean {
    if (!this.lastFailureTime) return false
    
    const timeSinceLastFailure = Date.now() - this.lastFailureTime.getTime()
    return timeSinceLastFailure >= this.config.recoveryTimeout!
  }

  /**
   * Transition to CLOSED state
   */
  private transitionToClosed(): void {
    const previousState = this.state
    this.state = CircuitBreakerState.CLOSED
    this.stateChangedAt = new Date()
    this.failureCount = 0
    this.halfOpenCallCount = 0
    
    console.log(`🟢 Circuit breaker '${this.name}' transitioned to CLOSED`)
    this.emitEvent({
      type: 'state_change',
      timestamp: new Date(),
      state: this.state,
      previousState
    })
  }

  /**
   * Transition to OPEN state
   */
  private transitionToOpen(): void {
    const previousState = this.state
    this.state = CircuitBreakerState.OPEN
    this.stateChangedAt = new Date()
    this.halfOpenCallCount = 0
    
    console.log(`🔴 Circuit breaker '${this.name}' transitioned to OPEN (failures: ${this.failureCount})`)
    this.emitEvent({
      type: 'state_change',
      timestamp: new Date(),
      state: this.state,
      previousState
    })
  }

  /**
   * Transition to HALF_OPEN state
   */
  private transitionToHalfOpen(): void {
    const previousState = this.state
    this.state = CircuitBreakerState.HALF_OPEN
    this.stateChangedAt = new Date()
    this.halfOpenCallCount = 0
    
    console.log(`🟡 Circuit breaker '${this.name}' transitioned to HALF_OPEN`)
    this.emitEvent({
      type: 'state_change',
      timestamp: new Date(),
      state: this.state,
      previousState
    })
  }

  /**
   * Initialize metrics
   */
  private initializeMetrics(): CircuitBreakerMetrics {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      timeouts: 0,
      state: this.state,
      stateChangedAt: this.stateChangedAt,
      failureRate: 0
    }
  }

  /**
   * Update metrics
   */
  private updateMetrics(): void {
    this.metrics.state = this.state
    this.metrics.stateChangedAt = this.stateChangedAt
    this.metrics.lastFailureTime = this.lastFailureTime
    this.metrics.lastSuccessTime = this.lastSuccessTime
    
    if (this.metrics.totalRequests > 0) {
      this.metrics.failureRate = this.metrics.failedRequests / this.metrics.totalRequests
    }
  }

  /**
   * Get current metrics
   */
  getMetrics(): CircuitBreakerMetrics {
    this.updateMetrics()
    return { ...this.metrics }
  }

  /**
   * Get current state
   */
  getState(): CircuitBreakerState {
    return this.state
  }

  /**
   * Reset circuit breaker
   */
  reset(): void {
    console.log(`🔄 Resetting circuit breaker '${this.name}'`)
    this.transitionToClosed()
    this.metrics = this.initializeMetrics()
  }

  /**
   * Force state change (for testing)
   */
  forceState(state: CircuitBreakerState): void {
    console.log(`⚠️ Forcing circuit breaker '${this.name}' to state: ${state}`)
    const previousState = this.state
    this.state = state
    this.stateChangedAt = new Date()
    
    this.emitEvent({
      type: 'state_change',
      timestamp: new Date(),
      state: this.state,
      previousState
    })
  }

  /**
   * Add event listener
   */
  onEvent(listener: (event: CircuitBreakerEvent) => void): void {
    this.eventListeners.push(listener)
  }

  /**
   * Remove event listener
   */
  removeEventListener(listener: (event: CircuitBreakerEvent) => void): void {
    const index = this.eventListeners.indexOf(listener)
    if (index > -1) {
      this.eventListeners.splice(index, 1)
    }
  }

  /**
   * Emit event to listeners
   */
  private emitEvent(event: CircuitBreakerEvent): void {
    this.eventListeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error(`Error in circuit breaker event listener:`, error)
      }
    })
  }

  /**
   * Health check
   */
  isHealthy(): boolean {
    return this.state === CircuitBreakerState.CLOSED || 
           this.state === CircuitBreakerState.HALF_OPEN
  }
}

// Circuit breaker registry for managing multiple circuit breakers
export class CircuitBreakerRegistry {
  private static instance: CircuitBreakerRegistry
  private circuitBreakers: Map<string, CircuitBreaker> = new Map()

  static getInstance(): CircuitBreakerRegistry {
    if (!CircuitBreakerRegistry.instance) {
      CircuitBreakerRegistry.instance = new CircuitBreakerRegistry()
    }
    return CircuitBreakerRegistry.instance
  }

  getOrCreate(name: string, config?: Partial<CircuitBreakerConfig>): CircuitBreaker {
    if (!this.circuitBreakers.has(name)) {
      this.circuitBreakers.set(name, new CircuitBreaker(name, config))
    }
    return this.circuitBreakers.get(name)!
  }

  get(name: string): CircuitBreaker | undefined {
    return this.circuitBreakers.get(name)
  }

  getAll(): Map<string, CircuitBreaker> {
    return new Map(this.circuitBreakers)
  }

  remove(name: string): boolean {
    return this.circuitBreakers.delete(name)
  }

  clear(): void {
    this.circuitBreakers.clear()
  }

  getHealthStatus(): Record<string, boolean> {
    const status: Record<string, boolean> = {}
    this.circuitBreakers.forEach((breaker, name) => {
      status[name] = breaker.isHealthy()
    })
    return status
  }
}

// Export singleton registry
export const circuitBreakerRegistry = CircuitBreakerRegistry.getInstance()
