// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// ============================================================================
// USER MANAGEMENT
// ============================================================================

model User {
  id     String  @id @default(cuid())
  email  String  @unique
  name   String?
  avatar String?

  // Authentication
  password      String?
  emailVerified DateTime?
  isActive      Boolean   @default(true)

  // Profile
  role         String @default("USER") // USER, ADMIN, SUPER_ADMIN
  subscription String @default("FREE") // FREE, BASIC, ENTHUSIAST, PRO

  // Token tracking
  tokenBalance Int @default(5)
  tokenUsed    Int @default(0)

  // Preferences
  preferences String? // JSON string

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  lastLoginAt DateTime?

  // Relations
  projects            Project[]
  images              Image[]
  processingJobs      ProcessingJob[]
  tokenTransactions   TokenTransaction[]
  auditLogs           AuditLog[]
  oauthAccounts       OAuthAccount[]
  analysisResults     AnalysisResult[]
  componentDetections ComponentDetection[]
  designTokens        DesignTokens[]

  @@map("users")
}

// Enums converted to string constants for SQLite compatibility
// UserRole: USER, ADMIN, SUPER_ADMIN
// SubscriptionTier: FREE, BASIC, ENTHUSIAST, PRO

model OAuthAccount {
  id                String    @id @default(cuid())
  userId            String
  provider          String // "google", "github"
  providerAccountId String
  accessToken       String?
  refreshToken      String?
  expiresAt         DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("oauth_accounts")
}

// ============================================================================
// PROJECT MANAGEMENT
// ============================================================================

model Project {
  id          String  @id @default(cuid())
  userId      String
  name        String
  description String?

  // Project settings
  settings   String? // JSON string
  isArchived Boolean @default(false)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  images Image[]

  @@map("projects")
}

// ============================================================================
// IMAGE MANAGEMENT
// ============================================================================

model Image {
  id               String  @id @default(cuid())
  userId           String
  projectId        String?
  filename         String
  originalFilename String
  mimeType         String
  size             Int

  // Image properties
  width      Int
  height     Int
  format     String?
  colorSpace String?

  // Processing mode and status
  mode             String @default("AUTO") // AUTO, MANUAL
  processingStatus String @default("UPLOADING") // UPLOADING, QUEUED, PROCESSING, COMPLETED, FAILED, CANCELLED

  // Storage
  s3Key        String
  s3Bucket     String
  previewUrl   String?
  thumbnailUrl String?

  // Metadata
  metadata String? // JSON string
  exifData String? // JSON string

  // Token cost tracking
  tokenCost Int @default(0)

  // Timestamps
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime?

  // Relations
  user                User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  project             Project?             @relation(fields: [projectId], references: [id], onDelete: SetNull)
  processingJobs      ProcessingJob[]
  analysisResults     AnalysisResult[]
  componentDetections ComponentDetection[]
  designTokens        DesignTokens[]

  @@map("images")
}

// ProcessingMode: AUTO, MANUAL
// ProcessingStatus: UPLOADING, QUEUED, PROCESSING, COMPLETED, FAILED, CANCELLED

// ============================================================================
// PROCESSING PIPELINE
// ============================================================================

model ProcessingJob {
  id      String @id @default(cuid())
  userId  String
  imageId String

  // Job details
  type     String // SEGMENTATION, CLASSIFICATION, LAYOUT_ANALYSIS, COMPONENT_MAPPING, STYLE_EXTRACTION, EXPORT_GENERATION
  status   String @default("PENDING") // PENDING, RUNNING, COMPLETED, FAILED, CANCELLED
  priority Int    @default(0)

  // Processing data
  input    String? // JSON string
  output   String? // JSON string
  error    String?
  progress Float   @default(0)

  // Resource tracking
  tokenCost      Int  @default(0)
  processingTime Int? // milliseconds

  // Queue management
  queuedAt    DateTime  @default(now())
  startedAt   DateTime?
  completedAt DateTime?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  image Image @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@map("processing_jobs")
}

// ProcessingJobType: SEGMENTATION, CLASSIFICATION, LAYOUT_ANALYSIS, COMPONENT_MAPPING, STYLE_EXTRACTION, EXPORT_GENERATION
// ProcessingJobStatus: PENDING, RUNNING, COMPLETED, FAILED, CANCELLED

// ============================================================================
// ANALYSIS RESULTS STORAGE
// ============================================================================

model AnalysisResult {
  id      String @id @default(cuid())
  imageId String
  userId  String

  // Analysis metadata
  analysisType   String // COMPONENT_DETECTION, LAYOUT_ANALYSIS, DESIGN_TOKENS, QUALITY_ASSESSMENT
  processingMode String // STANDARD, MULTI_STAGE
  version        String @default("1.0")

  // Results data
  results        String // JSON string containing analysis results
  confidence     Float? // Overall confidence score (0-1)
  processingTime Int? // Processing time in milliseconds

  // Quality metrics
  componentCount Int? // Number of detected components
  qualityScore   Float? // Overall quality score (0-100)

  // Cache and performance
  cacheKey String? // Redis cache key for fast retrieval
  cacheTtl Int? // Cache TTL in seconds

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  image Image @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@index([imageId, analysisType])
  @@index([userId, createdAt])
  @@map("analysis_results")
}

model ComponentDetection {
  id      String @id @default(cuid())
  imageId String
  userId  String

  // Component data
  components    String // JSON array of detected components
  hierarchy     String // JSON object representing component hierarchy
  boundingBoxes String // JSON array of bounding box coordinates

  // Detection metadata
  detectionModel String @default("gpt-4o-mini") // AI model used
  confidence     Float // Average confidence score
  processingTime Int // Processing time in milliseconds

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  image Image @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@index([imageId])
  @@index([userId, createdAt])
  @@map("component_detections")
}

model DesignTokens {
  id      String @id @default(cuid())
  imageId String
  userId  String

  // Design token data
  colors      String // JSON object with color tokens
  typography  String // JSON object with typography tokens
  spacing     String // JSON object with spacing tokens
  components  String // JSON object with component variants
  breakpoints String // JSON object with responsive breakpoints

  // Extraction metadata
  extractionMethod String @default("ai-analysis") // Method used for extraction
  confidence       Float // Confidence in extracted tokens
  tokenCount       Int // Total number of tokens extracted

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  image Image @relation(fields: [imageId], references: [id], onDelete: Cascade)

  @@index([imageId])
  @@index([userId, createdAt])
  @@map("design_tokens")
}

// ============================================================================
// TOKEN SYSTEM
// ============================================================================

model TokenTransaction {
  id     String @id @default(cuid())
  userId String

  // Transaction details
  type        String // PURCHASE, SUBSCRIPTION_CREDIT, PROCESSING_DEBIT, REFUND, BONUS, ADMIN_ADJUSTMENT
  amount      Int // positive for credits, negative for debits
  description String?

  // Context
  relatedId   String? // ID of related entity (job, subscription, etc.)
  relatedType String? // Type of related entity

  // Balance tracking
  balanceBefore Int
  balanceAfter  Int

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("token_transactions")
}

// TokenTransactionType: PURCHASE, SUBSCRIPTION_CREDIT, PROCESSING_DEBIT, REFUND, BONUS, ADMIN_ADJUSTMENT

// ============================================================================
// AUDIT & LOGGING
// ============================================================================

model AuditLog {
  id String @id @default(cuid())

  // Actor information
  userId    String?
  userEmail String?
  ipAddress String?
  userAgent String?

  // Action details
  action      String
  resource    String
  resourceId  String?
  description String?

  // Context data
  metadata String? // JSON string

  // Timestamps
  createdAt DateTime @default(now())

  // Relations
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}

// ============================================================================
// SYSTEM CONFIGURATION
// ============================================================================

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String // JSON string

  // Metadata
  description String?
  category    String?
  isPublic    Boolean @default(false)

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

// ============================================================================
// FEATURE FLAGS
// ============================================================================

model FeatureFlag {
  id          String  @id @default(cuid())
  name        String  @unique
  description String?

  // Flag configuration
  isEnabled         Boolean @default(false)
  rolloutPercentage Float   @default(0)

  // Targeting
  targetUsers String? // JSON string - Array of user IDs or criteria

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("feature_flags")
}
