import React, { useState } from 'react';
import InfiniteCanvas from '../components/Canvas/InfiniteCanvas';
import ComponentPropertiesPanel from '../components/Canvas/ComponentPropertiesPanel';
import { mockCanvasImage, createStressTestImage } from '../components/Canvas/CanvasTestData';
// Import types from CanvasTestData
import { ComponentDetection, CanvasImage } from '../components/Canvas/CanvasTestData';

const CanvasTestStandalone: React.FC = () => {
  const [imageData, setImageData] = useState<CanvasImage | null>(null);
  const [selectedComponent, setSelectedComponent] = useState<ComponentDetection | null>(null);
  const [showPropertiesPanel, setShowPropertiesPanel] = useState(true);

  const loadMockData = () => {
    setImageData(mockCanvasImage);
    setSelectedComponent(null);
  };

  const loadStressTest = () => {
    const stressData = createStressTestImage();
    setImageData(stressData);
    setSelectedComponent(null);
  };

  const clearCanvas = () => {
    setImageData(null);
    setSelectedComponent(null);
  };

  const handleComponentSelect = (component: ComponentDetection | null) => {
    setSelectedComponent(component);
  };

  const handleComponentUpdate = (componentId: string, updates: Partial<ComponentDetection>) => {
    if (!imageData) return;

    const updatedComponents = imageData.components.map(comp =>
      comp.id === componentId ? { ...comp, ...updates } : comp
    );

    setImageData({
      ...imageData,
      components: updatedComponents
    });

    // Update selected component if it's the one being updated
    if (selectedComponent && selectedComponent.id === componentId) {
      setSelectedComponent({ ...selectedComponent, ...updates });
    }
  };

  return (
    <div className="h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Canvas Test Environment (Standalone)</h1>
            <p className="text-sm text-gray-600 mt-1">
              Test the infinite canvas implementation without authentication
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={loadMockData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Load Mock Data
            </button>
            <button
              onClick={loadStressTest}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Stress Test
            </button>
            <button
              onClick={clearCanvas}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Clear Canvas
            </button>
            <button
              onClick={() => setShowPropertiesPanel(!showPropertiesPanel)}
              className={`px-4 py-2 rounded-lg transition-colors ${
                showPropertiesPanel 
                  ? 'bg-green-600 text-white hover:bg-green-700' 
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Properties Panel
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex">
        {/* Canvas Area */}
        <div className="flex-1 relative">
          {imageData ? (
            <InfiniteCanvas
              image={imageData}
              onComponentSelect={handleComponentSelect}
              onComponentUpdate={handleComponentUpdate}
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
              <div className="text-center text-white">
                <h3 className="text-xl font-semibold mb-2">Canvas Test Environment</h3>
                <p className="text-gray-300 mb-4">
                  Click "Load Mock Data" to start testing the canvas functionality
                </p>
                <div className="space-y-2 text-sm text-gray-400">
                  <p>• Test zoom and pan interactions</p>
                  <p>• Select and edit components</p>
                  <p>• Stress test with 50 components</p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Properties Panel */}
        {showPropertiesPanel && (
          <ComponentPropertiesPanel
            selectedComponent={selectedComponent}
            onComponentUpdate={handleComponentUpdate}
          />
        )}
      </div>

      {/* Status Bar */}
      <div className="bg-gray-50 border-t px-6 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>Components: {imageData?.components.length || 0}</span>
            <span>Selected: {selectedComponent ? selectedComponent.label : 'None'}</span>
          </div>
          <div className="flex items-center space-x-4">
            <span>Zoom: 100%</span>
            <span>Position: (0, 0)</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CanvasTestStandalone;
