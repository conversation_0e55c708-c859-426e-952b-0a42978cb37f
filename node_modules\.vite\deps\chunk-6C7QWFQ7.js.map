{"version": 3, "sources": ["../../scheduler/cjs/scheduler.development.js", "../../scheduler/index.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n\n          'use strict';\n\n/* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error());\n}\n          var enableSchedulerDebugging = false;\nvar enableProfiling = false;\nvar frameYieldMs = 5;\n\nfunction push(heap, node) {\n  var index = heap.length;\n  heap.push(node);\n  siftUp(heap, node, index);\n}\nfunction peek(heap) {\n  return heap.length === 0 ? null : heap[0];\n}\nfunction pop(heap) {\n  if (heap.length === 0) {\n    return null;\n  }\n\n  var first = heap[0];\n  var last = heap.pop();\n\n  if (last !== first) {\n    heap[0] = last;\n    siftDown(heap, last, 0);\n  }\n\n  return first;\n}\n\nfunction siftUp(heap, node, i) {\n  var index = i;\n\n  while (index > 0) {\n    var parentIndex = index - 1 >>> 1;\n    var parent = heap[parentIndex];\n\n    if (compare(parent, node) > 0) {\n      // The parent is larger. Swap positions.\n      heap[parentIndex] = node;\n      heap[index] = parent;\n      index = parentIndex;\n    } else {\n      // The parent is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction siftDown(heap, node, i) {\n  var index = i;\n  var length = heap.length;\n  var halfLength = length >>> 1;\n\n  while (index < halfLength) {\n    var leftIndex = (index + 1) * 2 - 1;\n    var left = heap[leftIndex];\n    var rightIndex = leftIndex + 1;\n    var right = heap[rightIndex]; // If the left or right node is smaller, swap with the smaller of those.\n\n    if (compare(left, node) < 0) {\n      if (rightIndex < length && compare(right, left) < 0) {\n        heap[index] = right;\n        heap[rightIndex] = node;\n        index = rightIndex;\n      } else {\n        heap[index] = left;\n        heap[leftIndex] = node;\n        index = leftIndex;\n      }\n    } else if (rightIndex < length && compare(right, node) < 0) {\n      heap[index] = right;\n      heap[rightIndex] = node;\n      index = rightIndex;\n    } else {\n      // Neither child is smaller. Exit.\n      return;\n    }\n  }\n}\n\nfunction compare(a, b) {\n  // Compare sort index first, then task id.\n  var diff = a.sortIndex - b.sortIndex;\n  return diff !== 0 ? diff : a.id - b.id;\n}\n\n// TODO: Use symbols?\nvar ImmediatePriority = 1;\nvar UserBlockingPriority = 2;\nvar NormalPriority = 3;\nvar LowPriority = 4;\nvar IdlePriority = 5;\n\nfunction markTaskErrored(task, ms) {\n}\n\n/* eslint-disable no-var */\n\nvar hasPerformanceNow = typeof performance === 'object' && typeof performance.now === 'function';\n\nif (hasPerformanceNow) {\n  var localPerformance = performance;\n\n  exports.unstable_now = function () {\n    return localPerformance.now();\n  };\n} else {\n  var localDate = Date;\n  var initialTime = localDate.now();\n\n  exports.unstable_now = function () {\n    return localDate.now() - initialTime;\n  };\n} // Max 31 bit integer. The max integer size in V8 for 32-bit systems.\n// Math.pow(2, 30) - 1\n// 0b111111111111111111111111111111\n\n\nvar maxSigned31BitInt = 1073741823; // Times out immediately\n\nvar IMMEDIATE_PRIORITY_TIMEOUT = -1; // Eventually times out\n\nvar USER_BLOCKING_PRIORITY_TIMEOUT = 250;\nvar NORMAL_PRIORITY_TIMEOUT = 5000;\nvar LOW_PRIORITY_TIMEOUT = 10000; // Never times out\n\nvar IDLE_PRIORITY_TIMEOUT = maxSigned31BitInt; // Tasks are stored on a min heap\n\nvar taskQueue = [];\nvar timerQueue = []; // Incrementing id counter. Used to maintain insertion order.\n\nvar taskIdCounter = 1; // Pausing the scheduler is useful for debugging.\nvar currentTask = null;\nvar currentPriorityLevel = NormalPriority; // This is set while performing work, to prevent re-entrance.\n\nvar isPerformingWork = false;\nvar isHostCallbackScheduled = false;\nvar isHostTimeoutScheduled = false; // Capture local references to native APIs, in case a polyfill overrides them.\n\nvar localSetTimeout = typeof setTimeout === 'function' ? setTimeout : null;\nvar localClearTimeout = typeof clearTimeout === 'function' ? clearTimeout : null;\nvar localSetImmediate = typeof setImmediate !== 'undefined' ? setImmediate : null; // IE and Node.js + jsdom\n\nvar isInputPending = typeof navigator !== 'undefined' && navigator.scheduling !== undefined && navigator.scheduling.isInputPending !== undefined ? navigator.scheduling.isInputPending.bind(navigator.scheduling) : null;\n\nfunction advanceTimers(currentTime) {\n  // Check for tasks that are no longer delayed and add them to the queue.\n  var timer = peek(timerQueue);\n\n  while (timer !== null) {\n    if (timer.callback === null) {\n      // Timer was cancelled.\n      pop(timerQueue);\n    } else if (timer.startTime <= currentTime) {\n      // Timer fired. Transfer to the task queue.\n      pop(timerQueue);\n      timer.sortIndex = timer.expirationTime;\n      push(taskQueue, timer);\n    } else {\n      // Remaining timers are pending.\n      return;\n    }\n\n    timer = peek(timerQueue);\n  }\n}\n\nfunction handleTimeout(currentTime) {\n  isHostTimeoutScheduled = false;\n  advanceTimers(currentTime);\n\n  if (!isHostCallbackScheduled) {\n    if (peek(taskQueue) !== null) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    } else {\n      var firstTimer = peek(timerQueue);\n\n      if (firstTimer !== null) {\n        requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n      }\n    }\n  }\n}\n\nfunction flushWork(hasTimeRemaining, initialTime) {\n\n\n  isHostCallbackScheduled = false;\n\n  if (isHostTimeoutScheduled) {\n    // We scheduled a timeout but it's no longer needed. Cancel it.\n    isHostTimeoutScheduled = false;\n    cancelHostTimeout();\n  }\n\n  isPerformingWork = true;\n  var previousPriorityLevel = currentPriorityLevel;\n\n  try {\n    if (enableProfiling) {\n      try {\n        return workLoop(hasTimeRemaining, initialTime);\n      } catch (error) {\n        if (currentTask !== null) {\n          var currentTime = exports.unstable_now();\n          markTaskErrored(currentTask, currentTime);\n          currentTask.isQueued = false;\n        }\n\n        throw error;\n      }\n    } else {\n      // No catch in prod code path.\n      return workLoop(hasTimeRemaining, initialTime);\n    }\n  } finally {\n    currentTask = null;\n    currentPriorityLevel = previousPriorityLevel;\n    isPerformingWork = false;\n  }\n}\n\nfunction workLoop(hasTimeRemaining, initialTime) {\n  var currentTime = initialTime;\n  advanceTimers(currentTime);\n  currentTask = peek(taskQueue);\n\n  while (currentTask !== null && !(enableSchedulerDebugging )) {\n    if (currentTask.expirationTime > currentTime && (!hasTimeRemaining || shouldYieldToHost())) {\n      // This currentTask hasn't expired, and we've reached the deadline.\n      break;\n    }\n\n    var callback = currentTask.callback;\n\n    if (typeof callback === 'function') {\n      currentTask.callback = null;\n      currentPriorityLevel = currentTask.priorityLevel;\n      var didUserCallbackTimeout = currentTask.expirationTime <= currentTime;\n\n      var continuationCallback = callback(didUserCallbackTimeout);\n      currentTime = exports.unstable_now();\n\n      if (typeof continuationCallback === 'function') {\n        currentTask.callback = continuationCallback;\n      } else {\n\n        if (currentTask === peek(taskQueue)) {\n          pop(taskQueue);\n        }\n      }\n\n      advanceTimers(currentTime);\n    } else {\n      pop(taskQueue);\n    }\n\n    currentTask = peek(taskQueue);\n  } // Return whether there's additional work\n\n\n  if (currentTask !== null) {\n    return true;\n  } else {\n    var firstTimer = peek(timerQueue);\n\n    if (firstTimer !== null) {\n      requestHostTimeout(handleTimeout, firstTimer.startTime - currentTime);\n    }\n\n    return false;\n  }\n}\n\nfunction unstable_runWithPriority(priorityLevel, eventHandler) {\n  switch (priorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n    case LowPriority:\n    case IdlePriority:\n      break;\n\n    default:\n      priorityLevel = NormalPriority;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_next(eventHandler) {\n  var priorityLevel;\n\n  switch (currentPriorityLevel) {\n    case ImmediatePriority:\n    case UserBlockingPriority:\n    case NormalPriority:\n      // Shift down to normal priority\n      priorityLevel = NormalPriority;\n      break;\n\n    default:\n      // Anything lower than normal priority should remain at the current level.\n      priorityLevel = currentPriorityLevel;\n      break;\n  }\n\n  var previousPriorityLevel = currentPriorityLevel;\n  currentPriorityLevel = priorityLevel;\n\n  try {\n    return eventHandler();\n  } finally {\n    currentPriorityLevel = previousPriorityLevel;\n  }\n}\n\nfunction unstable_wrapCallback(callback) {\n  var parentPriorityLevel = currentPriorityLevel;\n  return function () {\n    // This is a fork of runWithPriority, inlined for performance.\n    var previousPriorityLevel = currentPriorityLevel;\n    currentPriorityLevel = parentPriorityLevel;\n\n    try {\n      return callback.apply(this, arguments);\n    } finally {\n      currentPriorityLevel = previousPriorityLevel;\n    }\n  };\n}\n\nfunction unstable_scheduleCallback(priorityLevel, callback, options) {\n  var currentTime = exports.unstable_now();\n  var startTime;\n\n  if (typeof options === 'object' && options !== null) {\n    var delay = options.delay;\n\n    if (typeof delay === 'number' && delay > 0) {\n      startTime = currentTime + delay;\n    } else {\n      startTime = currentTime;\n    }\n  } else {\n    startTime = currentTime;\n  }\n\n  var timeout;\n\n  switch (priorityLevel) {\n    case ImmediatePriority:\n      timeout = IMMEDIATE_PRIORITY_TIMEOUT;\n      break;\n\n    case UserBlockingPriority:\n      timeout = USER_BLOCKING_PRIORITY_TIMEOUT;\n      break;\n\n    case IdlePriority:\n      timeout = IDLE_PRIORITY_TIMEOUT;\n      break;\n\n    case LowPriority:\n      timeout = LOW_PRIORITY_TIMEOUT;\n      break;\n\n    case NormalPriority:\n    default:\n      timeout = NORMAL_PRIORITY_TIMEOUT;\n      break;\n  }\n\n  var expirationTime = startTime + timeout;\n  var newTask = {\n    id: taskIdCounter++,\n    callback: callback,\n    priorityLevel: priorityLevel,\n    startTime: startTime,\n    expirationTime: expirationTime,\n    sortIndex: -1\n  };\n\n  if (startTime > currentTime) {\n    // This is a delayed task.\n    newTask.sortIndex = startTime;\n    push(timerQueue, newTask);\n\n    if (peek(taskQueue) === null && newTask === peek(timerQueue)) {\n      // All tasks are delayed, and this is the task with the earliest delay.\n      if (isHostTimeoutScheduled) {\n        // Cancel an existing timeout.\n        cancelHostTimeout();\n      } else {\n        isHostTimeoutScheduled = true;\n      } // Schedule a timeout.\n\n\n      requestHostTimeout(handleTimeout, startTime - currentTime);\n    }\n  } else {\n    newTask.sortIndex = expirationTime;\n    push(taskQueue, newTask);\n    // wait until the next time we yield.\n\n\n    if (!isHostCallbackScheduled && !isPerformingWork) {\n      isHostCallbackScheduled = true;\n      requestHostCallback(flushWork);\n    }\n  }\n\n  return newTask;\n}\n\nfunction unstable_pauseExecution() {\n}\n\nfunction unstable_continueExecution() {\n\n  if (!isHostCallbackScheduled && !isPerformingWork) {\n    isHostCallbackScheduled = true;\n    requestHostCallback(flushWork);\n  }\n}\n\nfunction unstable_getFirstCallbackNode() {\n  return peek(taskQueue);\n}\n\nfunction unstable_cancelCallback(task) {\n  // remove from the queue because you can't remove arbitrary nodes from an\n  // array based heap, only the first one.)\n\n\n  task.callback = null;\n}\n\nfunction unstable_getCurrentPriorityLevel() {\n  return currentPriorityLevel;\n}\n\nvar isMessageLoopRunning = false;\nvar scheduledHostCallback = null;\nvar taskTimeoutID = -1; // Scheduler periodically yields in case there is other work on the main\n// thread, like user events. By default, it yields multiple times per frame.\n// It does not attempt to align with frame boundaries, since most tasks don't\n// need to be frame aligned; for those that do, use requestAnimationFrame.\n\nvar frameInterval = frameYieldMs;\nvar startTime = -1;\n\nfunction shouldYieldToHost() {\n  var timeElapsed = exports.unstable_now() - startTime;\n\n  if (timeElapsed < frameInterval) {\n    // The main thread has only been blocked for a really short amount of time;\n    // smaller than a single frame. Don't yield yet.\n    return false;\n  } // The main thread has been blocked for a non-negligible amount of time. We\n\n\n  return true;\n}\n\nfunction requestPaint() {\n\n}\n\nfunction forceFrameRate(fps) {\n  if (fps < 0 || fps > 125) {\n    // Using console['error'] to evade Babel and ESLint\n    console['error']('forceFrameRate takes a positive int between 0 and 125, ' + 'forcing frame rates higher than 125 fps is not supported');\n    return;\n  }\n\n  if (fps > 0) {\n    frameInterval = Math.floor(1000 / fps);\n  } else {\n    // reset the framerate\n    frameInterval = frameYieldMs;\n  }\n}\n\nvar performWorkUntilDeadline = function () {\n  if (scheduledHostCallback !== null) {\n    var currentTime = exports.unstable_now(); // Keep track of the start time so we can measure how long the main thread\n    // has been blocked.\n\n    startTime = currentTime;\n    var hasTimeRemaining = true; // If a scheduler task throws, exit the current browser task so the\n    // error can be observed.\n    //\n    // Intentionally not using a try-catch, since that makes some debugging\n    // techniques harder. Instead, if `scheduledHostCallback` errors, then\n    // `hasMoreWork` will remain true, and we'll continue the work loop.\n\n    var hasMoreWork = true;\n\n    try {\n      hasMoreWork = scheduledHostCallback(hasTimeRemaining, currentTime);\n    } finally {\n      if (hasMoreWork) {\n        // If there's more work, schedule the next message event at the end\n        // of the preceding one.\n        schedulePerformWorkUntilDeadline();\n      } else {\n        isMessageLoopRunning = false;\n        scheduledHostCallback = null;\n      }\n    }\n  } else {\n    isMessageLoopRunning = false;\n  } // Yielding to the browser will give it a chance to paint, so we can\n};\n\nvar schedulePerformWorkUntilDeadline;\n\nif (typeof localSetImmediate === 'function') {\n  // Node.js and old IE.\n  // There's a few reasons for why we prefer setImmediate.\n  //\n  // Unlike MessageChannel, it doesn't prevent a Node.js process from exiting.\n  // (Even though this is a DOM fork of the Scheduler, you could get here\n  // with a mix of Node.js 15+, which has a MessageChannel, and jsdom.)\n  // https://github.com/facebook/react/issues/20756\n  //\n  // But also, it runs earlier which is the semantic we want.\n  // If other browsers ever implement it, it's better to use it.\n  // Although both of these would be inferior to native scheduling.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetImmediate(performWorkUntilDeadline);\n  };\n} else if (typeof MessageChannel !== 'undefined') {\n  // DOM and Worker environments.\n  // We prefer MessageChannel because of the 4ms setTimeout clamping.\n  var channel = new MessageChannel();\n  var port = channel.port2;\n  channel.port1.onmessage = performWorkUntilDeadline;\n\n  schedulePerformWorkUntilDeadline = function () {\n    port.postMessage(null);\n  };\n} else {\n  // We should only fallback here in non-browser environments.\n  schedulePerformWorkUntilDeadline = function () {\n    localSetTimeout(performWorkUntilDeadline, 0);\n  };\n}\n\nfunction requestHostCallback(callback) {\n  scheduledHostCallback = callback;\n\n  if (!isMessageLoopRunning) {\n    isMessageLoopRunning = true;\n    schedulePerformWorkUntilDeadline();\n  }\n}\n\nfunction requestHostTimeout(callback, ms) {\n  taskTimeoutID = localSetTimeout(function () {\n    callback(exports.unstable_now());\n  }, ms);\n}\n\nfunction cancelHostTimeout() {\n  localClearTimeout(taskTimeoutID);\n  taskTimeoutID = -1;\n}\n\nvar unstable_requestPaint = requestPaint;\nvar unstable_Profiling =  null;\n\nexports.unstable_IdlePriority = IdlePriority;\nexports.unstable_ImmediatePriority = ImmediatePriority;\nexports.unstable_LowPriority = LowPriority;\nexports.unstable_NormalPriority = NormalPriority;\nexports.unstable_Profiling = unstable_Profiling;\nexports.unstable_UserBlockingPriority = UserBlockingPriority;\nexports.unstable_cancelCallback = unstable_cancelCallback;\nexports.unstable_continueExecution = unstable_continueExecution;\nexports.unstable_forceFrameRate = forceFrameRate;\nexports.unstable_getCurrentPriorityLevel = unstable_getCurrentPriorityLevel;\nexports.unstable_getFirstCallbackNode = unstable_getFirstCallbackNode;\nexports.unstable_next = unstable_next;\nexports.unstable_pauseExecution = unstable_pauseExecution;\nexports.unstable_requestPaint = unstable_requestPaint;\nexports.unstable_runWithPriority = unstable_runWithPriority;\nexports.unstable_scheduleCallback = unstable_scheduleCallback;\nexports.unstable_shouldYield = shouldYieldToHost;\nexports.unstable_wrapCallback = unstable_wrapCallback;\n          /* global __REACT_DEVTOOLS_GLOBAL_HOOK__ */\nif (\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ !== 'undefined' &&\n  typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop ===\n    'function'\n) {\n  __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error());\n}\n        \n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.min.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAAA;AAYA,QAAI,MAAuC;AACzC,OAAC,WAAW;AAEJ;AAGV,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,gCACpC,YACF;AACA,yCAA+B,4BAA4B,IAAI,MAAM,CAAC;AAAA,QACxE;AACU,YAAI,2BAA2B;AACzC,YAAI,kBAAkB;AACtB,YAAI,eAAe;AAEnB,iBAAS,KAAK,MAAM,MAAM;AACxB,cAAI,QAAQ,KAAK;AACjB,eAAK,KAAK,IAAI;AACd,iBAAO,MAAM,MAAM,KAAK;AAAA,QAC1B;AACA,iBAAS,KAAK,MAAM;AAClB,iBAAO,KAAK,WAAW,IAAI,OAAO,KAAK,CAAC;AAAA,QAC1C;AACA,iBAAS,IAAI,MAAM;AACjB,cAAI,KAAK,WAAW,GAAG;AACrB,mBAAO;AAAA,UACT;AAEA,cAAI,QAAQ,KAAK,CAAC;AAClB,cAAI,OAAO,KAAK,IAAI;AAEpB,cAAI,SAAS,OAAO;AAClB,iBAAK,CAAC,IAAI;AACV,qBAAS,MAAM,MAAM,CAAC;AAAA,UACxB;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,OAAO,MAAM,MAAM,GAAG;AAC7B,cAAI,QAAQ;AAEZ,iBAAO,QAAQ,GAAG;AAChB,gBAAI,cAAc,QAAQ,MAAM;AAChC,gBAAI,SAAS,KAAK,WAAW;AAE7B,gBAAI,QAAQ,QAAQ,IAAI,IAAI,GAAG;AAE7B,mBAAK,WAAW,IAAI;AACpB,mBAAK,KAAK,IAAI;AACd,sBAAQ;AAAA,YACV,OAAO;AAEL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,SAAS,MAAM,MAAM,GAAG;AAC/B,cAAI,QAAQ;AACZ,cAAI,SAAS,KAAK;AAClB,cAAI,aAAa,WAAW;AAE5B,iBAAO,QAAQ,YAAY;AACzB,gBAAI,aAAa,QAAQ,KAAK,IAAI;AAClC,gBAAI,OAAO,KAAK,SAAS;AACzB,gBAAI,aAAa,YAAY;AAC7B,gBAAI,QAAQ,KAAK,UAAU;AAE3B,gBAAI,QAAQ,MAAM,IAAI,IAAI,GAAG;AAC3B,kBAAI,aAAa,UAAU,QAAQ,OAAO,IAAI,IAAI,GAAG;AACnD,qBAAK,KAAK,IAAI;AACd,qBAAK,UAAU,IAAI;AACnB,wBAAQ;AAAA,cACV,OAAO;AACL,qBAAK,KAAK,IAAI;AACd,qBAAK,SAAS,IAAI;AAClB,wBAAQ;AAAA,cACV;AAAA,YACF,WAAW,aAAa,UAAU,QAAQ,OAAO,IAAI,IAAI,GAAG;AAC1D,mBAAK,KAAK,IAAI;AACd,mBAAK,UAAU,IAAI;AACnB,sBAAQ;AAAA,YACV,OAAO;AAEL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,QAAQ,GAAG,GAAG;AAErB,cAAI,OAAO,EAAE,YAAY,EAAE;AAC3B,iBAAO,SAAS,IAAI,OAAO,EAAE,KAAK,EAAE;AAAA,QACtC;AAGA,YAAI,oBAAoB;AACxB,YAAI,uBAAuB;AAC3B,YAAI,iBAAiB;AACrB,YAAI,cAAc;AAClB,YAAI,eAAe;AAEnB,iBAAS,gBAAgB,MAAM,IAAI;AAAA,QACnC;AAIA,YAAI,oBAAoB,OAAO,gBAAgB,YAAY,OAAO,YAAY,QAAQ;AAEtF,YAAI,mBAAmB;AACrB,cAAI,mBAAmB;AAEvB,kBAAQ,eAAe,WAAY;AACjC,mBAAO,iBAAiB,IAAI;AAAA,UAC9B;AAAA,QACF,OAAO;AACL,cAAI,YAAY;AAChB,cAAI,cAAc,UAAU,IAAI;AAEhC,kBAAQ,eAAe,WAAY;AACjC,mBAAO,UAAU,IAAI,IAAI;AAAA,UAC3B;AAAA,QACF;AAKA,YAAI,oBAAoB;AAExB,YAAI,6BAA6B;AAEjC,YAAI,iCAAiC;AACrC,YAAI,0BAA0B;AAC9B,YAAI,uBAAuB;AAE3B,YAAI,wBAAwB;AAE5B,YAAI,YAAY,CAAC;AACjB,YAAI,aAAa,CAAC;AAElB,YAAI,gBAAgB;AACpB,YAAI,cAAc;AAClB,YAAI,uBAAuB;AAE3B,YAAI,mBAAmB;AACvB,YAAI,0BAA0B;AAC9B,YAAI,yBAAyB;AAE7B,YAAI,kBAAkB,OAAO,eAAe,aAAa,aAAa;AACtE,YAAI,oBAAoB,OAAO,iBAAiB,aAAa,eAAe;AAC5E,YAAI,oBAAoB,OAAO,iBAAiB,cAAc,eAAe;AAE7E,YAAI,iBAAiB,OAAO,cAAc,eAAe,UAAU,eAAe,UAAa,UAAU,WAAW,mBAAmB,SAAY,UAAU,WAAW,eAAe,KAAK,UAAU,UAAU,IAAI;AAEpN,iBAAS,cAAc,aAAa;AAElC,cAAI,QAAQ,KAAK,UAAU;AAE3B,iBAAO,UAAU,MAAM;AACrB,gBAAI,MAAM,aAAa,MAAM;AAE3B,kBAAI,UAAU;AAAA,YAChB,WAAW,MAAM,aAAa,aAAa;AAEzC,kBAAI,UAAU;AACd,oBAAM,YAAY,MAAM;AACxB,mBAAK,WAAW,KAAK;AAAA,YACvB,OAAO;AAEL;AAAA,YACF;AAEA,oBAAQ,KAAK,UAAU;AAAA,UACzB;AAAA,QACF;AAEA,iBAAS,cAAc,aAAa;AAClC,mCAAyB;AACzB,wBAAc,WAAW;AAEzB,cAAI,CAAC,yBAAyB;AAC5B,gBAAI,KAAK,SAAS,MAAM,MAAM;AAC5B,wCAA0B;AAC1B,kCAAoB,SAAS;AAAA,YAC/B,OAAO;AACL,kBAAI,aAAa,KAAK,UAAU;AAEhC,kBAAI,eAAe,MAAM;AACvB,mCAAmB,eAAe,WAAW,YAAY,WAAW;AAAA,cACtE;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,UAAU,kBAAkBA,cAAa;AAGhD,oCAA0B;AAE1B,cAAI,wBAAwB;AAE1B,qCAAyB;AACzB,8BAAkB;AAAA,UACpB;AAEA,6BAAmB;AACnB,cAAI,wBAAwB;AAE5B,cAAI;AACF,gBAAI,iBAAiB;AACnB,kBAAI;AACF,uBAAO,SAAS,kBAAkBA,YAAW;AAAA,cAC/C,SAAS,OAAO;AACd,oBAAI,gBAAgB,MAAM;AACxB,sBAAI,cAAc,QAAQ,aAAa;AACvC,kCAAgB,aAAa,WAAW;AACxC,8BAAY,WAAW;AAAA,gBACzB;AAEA,sBAAM;AAAA,cACR;AAAA,YACF,OAAO;AAEL,qBAAO,SAAS,kBAAkBA,YAAW;AAAA,YAC/C;AAAA,UACF,UAAE;AACA,0BAAc;AACd,mCAAuB;AACvB,+BAAmB;AAAA,UACrB;AAAA,QACF;AAEA,iBAAS,SAAS,kBAAkBA,cAAa;AAC/C,cAAI,cAAcA;AAClB,wBAAc,WAAW;AACzB,wBAAc,KAAK,SAAS;AAE5B,iBAAO,gBAAgB,QAAQ,CAAE,0BAA4B;AAC3D,gBAAI,YAAY,iBAAiB,gBAAgB,CAAC,oBAAoB,kBAAkB,IAAI;AAE1F;AAAA,YACF;AAEA,gBAAI,WAAW,YAAY;AAE3B,gBAAI,OAAO,aAAa,YAAY;AAClC,0BAAY,WAAW;AACvB,qCAAuB,YAAY;AACnC,kBAAI,yBAAyB,YAAY,kBAAkB;AAE3D,kBAAI,uBAAuB,SAAS,sBAAsB;AAC1D,4BAAc,QAAQ,aAAa;AAEnC,kBAAI,OAAO,yBAAyB,YAAY;AAC9C,4BAAY,WAAW;AAAA,cACzB,OAAO;AAEL,oBAAI,gBAAgB,KAAK,SAAS,GAAG;AACnC,sBAAI,SAAS;AAAA,gBACf;AAAA,cACF;AAEA,4BAAc,WAAW;AAAA,YAC3B,OAAO;AACL,kBAAI,SAAS;AAAA,YACf;AAEA,0BAAc,KAAK,SAAS;AAAA,UAC9B;AAGA,cAAI,gBAAgB,MAAM;AACxB,mBAAO;AAAA,UACT,OAAO;AACL,gBAAI,aAAa,KAAK,UAAU;AAEhC,gBAAI,eAAe,MAAM;AACvB,iCAAmB,eAAe,WAAW,YAAY,WAAW;AAAA,YACtE;AAEA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,yBAAyB,eAAe,cAAc;AAC7D,kBAAQ,eAAe;AAAA,YACrB,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AACH;AAAA,YAEF;AACE,8BAAgB;AAAA,UACpB;AAEA,cAAI,wBAAwB;AAC5B,iCAAuB;AAEvB,cAAI;AACF,mBAAO,aAAa;AAAA,UACtB,UAAE;AACA,mCAAuB;AAAA,UACzB;AAAA,QACF;AAEA,iBAAS,cAAc,cAAc;AACnC,cAAI;AAEJ,kBAAQ,sBAAsB;AAAA,YAC5B,KAAK;AAAA,YACL,KAAK;AAAA,YACL,KAAK;AAEH,8BAAgB;AAChB;AAAA,YAEF;AAEE,8BAAgB;AAChB;AAAA,UACJ;AAEA,cAAI,wBAAwB;AAC5B,iCAAuB;AAEvB,cAAI;AACF,mBAAO,aAAa;AAAA,UACtB,UAAE;AACA,mCAAuB;AAAA,UACzB;AAAA,QACF;AAEA,iBAAS,sBAAsB,UAAU;AACvC,cAAI,sBAAsB;AAC1B,iBAAO,WAAY;AAEjB,gBAAI,wBAAwB;AAC5B,mCAAuB;AAEvB,gBAAI;AACF,qBAAO,SAAS,MAAM,MAAM,SAAS;AAAA,YACvC,UAAE;AACA,qCAAuB;AAAA,YACzB;AAAA,UACF;AAAA,QACF;AAEA,iBAAS,0BAA0B,eAAe,UAAU,SAAS;AACnE,cAAI,cAAc,QAAQ,aAAa;AACvC,cAAIC;AAEJ,cAAI,OAAO,YAAY,YAAY,YAAY,MAAM;AACnD,gBAAI,QAAQ,QAAQ;AAEpB,gBAAI,OAAO,UAAU,YAAY,QAAQ,GAAG;AAC1C,cAAAA,aAAY,cAAc;AAAA,YAC5B,OAAO;AACL,cAAAA,aAAY;AAAA,YACd;AAAA,UACF,OAAO;AACL,YAAAA,aAAY;AAAA,UACd;AAEA,cAAI;AAEJ,kBAAQ,eAAe;AAAA,YACrB,KAAK;AACH,wBAAU;AACV;AAAA,YAEF,KAAK;AACH,wBAAU;AACV;AAAA,YAEF,KAAK;AACH,wBAAU;AACV;AAAA,YAEF,KAAK;AACH,wBAAU;AACV;AAAA,YAEF,KAAK;AAAA,YACL;AACE,wBAAU;AACV;AAAA,UACJ;AAEA,cAAI,iBAAiBA,aAAY;AACjC,cAAI,UAAU;AAAA,YACZ,IAAI;AAAA,YACJ;AAAA,YACA;AAAA,YACA,WAAWA;AAAA,YACX;AAAA,YACA,WAAW;AAAA,UACb;AAEA,cAAIA,aAAY,aAAa;AAE3B,oBAAQ,YAAYA;AACpB,iBAAK,YAAY,OAAO;AAExB,gBAAI,KAAK,SAAS,MAAM,QAAQ,YAAY,KAAK,UAAU,GAAG;AAE5D,kBAAI,wBAAwB;AAE1B,kCAAkB;AAAA,cACpB,OAAO;AACL,yCAAyB;AAAA,cAC3B;AAGA,iCAAmB,eAAeA,aAAY,WAAW;AAAA,YAC3D;AAAA,UACF,OAAO;AACL,oBAAQ,YAAY;AACpB,iBAAK,WAAW,OAAO;AAIvB,gBAAI,CAAC,2BAA2B,CAAC,kBAAkB;AACjD,wCAA0B;AAC1B,kCAAoB,SAAS;AAAA,YAC/B;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,iBAAS,0BAA0B;AAAA,QACnC;AAEA,iBAAS,6BAA6B;AAEpC,cAAI,CAAC,2BAA2B,CAAC,kBAAkB;AACjD,sCAA0B;AAC1B,gCAAoB,SAAS;AAAA,UAC/B;AAAA,QACF;AAEA,iBAAS,gCAAgC;AACvC,iBAAO,KAAK,SAAS;AAAA,QACvB;AAEA,iBAAS,wBAAwB,MAAM;AAKrC,eAAK,WAAW;AAAA,QAClB;AAEA,iBAAS,mCAAmC;AAC1C,iBAAO;AAAA,QACT;AAEA,YAAI,uBAAuB;AAC3B,YAAI,wBAAwB;AAC5B,YAAI,gBAAgB;AAKpB,YAAI,gBAAgB;AACpB,YAAI,YAAY;AAEhB,iBAAS,oBAAoB;AAC3B,cAAI,cAAc,QAAQ,aAAa,IAAI;AAE3C,cAAI,cAAc,eAAe;AAG/B,mBAAO;AAAA,UACT;AAGA,iBAAO;AAAA,QACT;AAEA,iBAAS,eAAe;AAAA,QAExB;AAEA,iBAAS,eAAe,KAAK;AAC3B,cAAI,MAAM,KAAK,MAAM,KAAK;AAExB,oBAAQ,OAAO,EAAE,iHAAsH;AACvI;AAAA,UACF;AAEA,cAAI,MAAM,GAAG;AACX,4BAAgB,KAAK,MAAM,MAAO,GAAG;AAAA,UACvC,OAAO;AAEL,4BAAgB;AAAA,UAClB;AAAA,QACF;AAEA,YAAI,2BAA2B,WAAY;AACzC,cAAI,0BAA0B,MAAM;AAClC,gBAAI,cAAc,QAAQ,aAAa;AAGvC,wBAAY;AACZ,gBAAI,mBAAmB;AAOvB,gBAAI,cAAc;AAElB,gBAAI;AACF,4BAAc,sBAAsB,kBAAkB,WAAW;AAAA,YACnE,UAAE;AACA,kBAAI,aAAa;AAGf,iDAAiC;AAAA,cACnC,OAAO;AACL,uCAAuB;AACvB,wCAAwB;AAAA,cAC1B;AAAA,YACF;AAAA,UACF,OAAO;AACL,mCAAuB;AAAA,UACzB;AAAA,QACF;AAEA,YAAI;AAEJ,YAAI,OAAO,sBAAsB,YAAY;AAY3C,6CAAmC,WAAY;AAC7C,8BAAkB,wBAAwB;AAAA,UAC5C;AAAA,QACF,WAAW,OAAO,mBAAmB,aAAa;AAGhD,cAAI,UAAU,IAAI,eAAe;AACjC,cAAI,OAAO,QAAQ;AACnB,kBAAQ,MAAM,YAAY;AAE1B,6CAAmC,WAAY;AAC7C,iBAAK,YAAY,IAAI;AAAA,UACvB;AAAA,QACF,OAAO;AAEL,6CAAmC,WAAY;AAC7C,4BAAgB,0BAA0B,CAAC;AAAA,UAC7C;AAAA,QACF;AAEA,iBAAS,oBAAoB,UAAU;AACrC,kCAAwB;AAExB,cAAI,CAAC,sBAAsB;AACzB,mCAAuB;AACvB,6CAAiC;AAAA,UACnC;AAAA,QACF;AAEA,iBAAS,mBAAmB,UAAU,IAAI;AACxC,0BAAgB,gBAAgB,WAAY;AAC1C,qBAAS,QAAQ,aAAa,CAAC;AAAA,UACjC,GAAG,EAAE;AAAA,QACP;AAEA,iBAAS,oBAAoB;AAC3B,4BAAkB,aAAa;AAC/B,0BAAgB;AAAA,QAClB;AAEA,YAAI,wBAAwB;AAC5B,YAAI,qBAAsB;AAE1B,gBAAQ,wBAAwB;AAChC,gBAAQ,6BAA6B;AACrC,gBAAQ,uBAAuB;AAC/B,gBAAQ,0BAA0B;AAClC,gBAAQ,qBAAqB;AAC7B,gBAAQ,gCAAgC;AACxC,gBAAQ,0BAA0B;AAClC,gBAAQ,6BAA6B;AACrC,gBAAQ,0BAA0B;AAClC,gBAAQ,mCAAmC;AAC3C,gBAAQ,gCAAgC;AACxC,gBAAQ,gBAAgB;AACxB,gBAAQ,0BAA0B;AAClC,gBAAQ,wBAAwB;AAChC,gBAAQ,2BAA2B;AACnC,gBAAQ,4BAA4B;AACpC,gBAAQ,uBAAuB;AAC/B,gBAAQ,wBAAwB;AAEhC,YACE,OAAO,mCAAmC,eAC1C,OAAO,+BAA+B,+BACpC,YACF;AACA,yCAA+B,2BAA2B,IAAI,MAAM,CAAC;AAAA,QACvE;AAAA,MAEE,GAAG;AAAA,IACL;AAAA;AAAA;;;ACznBA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;", "names": ["initialTime", "startTime"]}