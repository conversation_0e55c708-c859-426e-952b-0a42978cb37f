/**
 * Fallback Strategy Manager
 * 
 * Provides multiple fallback strategies for graceful failure handling
 * including mock data, cached responses, and degraded functionality.
 */

import { FallbackStrategy, FallbackConfig, AppError, ErrorSeverity } from './types'
import { RedisService } from '../redis'

// Fallback result
export interface FallbackResult<T> {
  success: boolean
  data?: T
  strategy: FallbackStrategy
  source: string
  timestamp: Date
  isFallback: boolean
  originalError?: Error
}

// Fallback context
export interface FallbackContext {
  operationName: string
  originalError: Error
  attemptNumber: number
  userId?: string
  metadata?: Record<string, any>
}

// Mock data provider function type
export type MockDataProvider<T> = (context: FallbackContext) => T | Promise<T>

// Cache provider interface
export interface CacheProvider {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  has(key: string): Promise<boolean>
  delete(key: string): Promise<void>
}

// Redis cache provider implementation
class RedisCacheProvider implements CacheProvider {
  constructor(private redis: typeof RedisService) {}

  async get<T>(key: string): Promise<T | null> {
    try {
      if (!this.redis.isReady) return null
      
      const data = await this.redis.client?.get(key)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('Redis cache get error:', error)
      return null
    }
  }

  async set<T>(key: string, value: T, ttl: number = 3600): Promise<void> {
    try {
      if (!this.redis.isReady) return
      
      await this.redis.client?.setex(key, ttl, JSON.stringify(value))
    } catch (error) {
      console.error('Redis cache set error:', error)
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      if (!this.redis.isReady) return false
      
      const exists = await this.redis.client?.exists(key)
      return exists === 1
    } catch (error) {
      console.error('Redis cache has error:', error)
      return false
    }
  }

  async delete(key: string): Promise<void> {
    try {
      if (!this.redis.isReady) return
      
      await this.redis.client?.del(key)
    } catch (error) {
      console.error('Redis cache delete error:', error)
    }
  }
}

export class FallbackManager {
  private cacheProvider: CacheProvider
  private mockDataProviders: Map<string, MockDataProvider<any>> = new Map()
  private alternativeServices: Map<string, string> = new Map()

  private defaultConfig: FallbackConfig = {
    strategy: FallbackStrategy.NONE,
    enabled: true,
    timeout: 5000,
  }

  constructor() {
    this.cacheProvider = new RedisCacheProvider(RedisService)
  }

  /**
   * Execute fallback strategy for a failed operation
   */
  async execute<T>(
    context: FallbackContext,
    config: Partial<FallbackConfig> = {}
  ): Promise<FallbackResult<T>> {
    const finalConfig = { ...this.defaultConfig, ...config }

    if (!finalConfig.enabled) {
      return this.createFailureResult<T>(context, 'Fallback disabled')
    }

    console.log(`🔄 Executing fallback strategy '${finalConfig.strategy}' for operation: ${context.operationName}`)

    try {
      switch (finalConfig.strategy) {
        case FallbackStrategy.MOCK_DATA:
          return await this.executeMockDataFallback<T>(context, finalConfig)

        case FallbackStrategy.CACHED_RESPONSE:
          return await this.executeCachedResponseFallback<T>(context, finalConfig)

        case FallbackStrategy.DEGRADED_FUNCTIONALITY:
          return await this.executeDegradedFunctionalityFallback<T>(context, finalConfig)

        case FallbackStrategy.ALTERNATIVE_SERVICE:
          return await this.executeAlternativeServiceFallback<T>(context, finalConfig)

        case FallbackStrategy.MANUAL_INTERVENTION:
          return await this.executeManualInterventionFallback<T>(context, finalConfig)

        case FallbackStrategy.NONE:
        default:
          return this.createFailureResult<T>(context, 'No fallback strategy configured')
      }
    } catch (error) {
      console.error(`❌ Fallback strategy '${finalConfig.strategy}' failed:`, error)
      return this.createFailureResult<T>(context, `Fallback execution failed: ${(error as Error).message}`)
    }
  }

  /**
   * Execute mock data fallback
   */
  private async executeMockDataFallback<T>(
    context: FallbackContext,
    config: FallbackConfig
  ): Promise<FallbackResult<T>> {
    const provider = this.mockDataProviders.get(context.operationName)
    
    if (!provider) {
      return this.createFailureResult<T>(context, 'No mock data provider configured')
    }

    try {
      const mockData = await provider(context)
      
      console.log(`✅ Mock data fallback successful for: ${context.operationName}`)
      
      return {
        success: true,
        data: mockData,
        strategy: FallbackStrategy.MOCK_DATA,
        source: 'mock_provider',
        timestamp: new Date(),
        isFallback: true,
        originalError: context.originalError
      }
    } catch (error) {
      return this.createFailureResult<T>(context, `Mock data provider failed: ${(error as Error).message}`)
    }
  }

  /**
   * Execute cached response fallback
   */
  private async executeCachedResponseFallback<T>(
    context: FallbackContext,
    config: FallbackConfig
  ): Promise<FallbackResult<T>> {
    if (!config.cacheKey) {
      return this.createFailureResult<T>(context, 'No cache key provided')
    }

    try {
      const cachedData = await this.cacheProvider.get<T>(config.cacheKey)
      
      if (cachedData) {
        console.log(`✅ Cached response fallback successful for: ${context.operationName}`)
        
        return {
          success: true,
          data: cachedData,
          strategy: FallbackStrategy.CACHED_RESPONSE,
          source: 'cache',
          timestamp: new Date(),
          isFallback: true,
          originalError: context.originalError
        }
      } else {
        return this.createFailureResult<T>(context, 'No cached data available')
      }
    } catch (error) {
      return this.createFailureResult<T>(context, `Cache access failed: ${(error as Error).message}`)
    }
  }

  /**
   * Execute degraded functionality fallback
   */
  private async executeDegradedFunctionalityFallback<T>(
    context: FallbackContext,
    config: FallbackConfig
  ): Promise<FallbackResult<T>> {
    // Provide minimal functionality based on operation type
    let degradedData: any

    switch (context.operationName) {
      case 'image-analysis':
        degradedData = {
          components: [],
          hierarchy: {},
          metadata: {
            imageWidth: 0,
            imageHeight: 0,
            processingTime: 0,
            modelUsed: 'degraded',
            confidence: 0
          },
          degraded: true,
          message: 'Analysis service temporarily unavailable. Please try again later.'
        }
        break

      case 'user-dashboard':
        degradedData = {
          images: [],
          stats: { total: 0, processed: 0, failed: 0 },
          degraded: true,
          message: 'Dashboard data temporarily unavailable.'
        }
        break

      default:
        degradedData = {
          degraded: true,
          message: 'Service temporarily unavailable. Limited functionality provided.'
        }
    }

    console.log(`✅ Degraded functionality fallback for: ${context.operationName}`)

    return {
      success: true,
      data: degradedData as T,
      strategy: FallbackStrategy.DEGRADED_FUNCTIONALITY,
      source: 'degraded_service',
      timestamp: new Date(),
      isFallback: true,
      originalError: context.originalError
    }
  }

  /**
   * Execute alternative service fallback
   */
  private async executeAlternativeServiceFallback<T>(
    context: FallbackContext,
    config: FallbackConfig
  ): Promise<FallbackResult<T>> {
    const alternativeUrl = config.alternativeServiceUrl || 
                          this.alternativeServices.get(context.operationName)

    if (!alternativeUrl) {
      return this.createFailureResult<T>(context, 'No alternative service configured')
    }

    try {
      // This would typically make an HTTP request to the alternative service
      // For now, we'll simulate this
      console.log(`🔄 Attempting alternative service: ${alternativeUrl}`)
      
      // Simulate alternative service call
      await new Promise(resolve => setTimeout(resolve, 100))
      
      return this.createFailureResult<T>(context, 'Alternative service not implemented yet')
    } catch (error) {
      return this.createFailureResult<T>(context, `Alternative service failed: ${(error as Error).message}`)
    }
  }

  /**
   * Execute manual intervention fallback
   */
  private async executeManualInterventionFallback<T>(
    context: FallbackContext,
    config: FallbackConfig
  ): Promise<FallbackResult<T>> {
    // Log for manual intervention and provide user-friendly message
    console.error(`🚨 Manual intervention required for: ${context.operationName}`, {
      error: context.originalError.message,
      userId: context.userId,
      metadata: context.metadata
    })

    const interventionData = {
      requiresManualIntervention: true,
      message: 'This request requires manual review. Our team has been notified and will assist you shortly.',
      supportTicketId: `TICKET-${Date.now()}`,
      estimatedResolutionTime: '2-4 hours'
    }

    return {
      success: true,
      data: interventionData as T,
      strategy: FallbackStrategy.MANUAL_INTERVENTION,
      source: 'manual_intervention',
      timestamp: new Date(),
      isFallback: true,
      originalError: context.originalError
    }
  }

  /**
   * Create failure result
   */
  private createFailureResult<T>(context: FallbackContext, reason: string): FallbackResult<T> {
    return {
      success: false,
      strategy: FallbackStrategy.NONE,
      source: 'fallback_manager',
      timestamp: new Date(),
      isFallback: true,
      originalError: new AppError(
        reason,
        'FALLBACK_FAILED',
        'internal' as any,
        ErrorSeverity.HIGH,
        false
      )
    }
  }

  /**
   * Register mock data provider
   */
  registerMockDataProvider<T>(operationName: string, provider: MockDataProvider<T>): void {
    this.mockDataProviders.set(operationName, provider)
    console.log(`📝 Registered mock data provider for: ${operationName}`)
  }

  /**
   * Register alternative service
   */
  registerAlternativeService(operationName: string, serviceUrl: string): void {
    this.alternativeServices.set(operationName, serviceUrl)
    console.log(`🔗 Registered alternative service for ${operationName}: ${serviceUrl}`)
  }

  /**
   * Cache successful response for future fallback use
   */
  async cacheResponse<T>(key: string, data: T, ttl: number = 3600): Promise<void> {
    try {
      await this.cacheProvider.set(key, data, ttl)
      console.log(`💾 Cached response for key: ${key}`)
    } catch (error) {
      console.error('Failed to cache response:', error)
    }
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<{ available: boolean; provider: string }> {
    return {
      available: await this.cacheProvider.has('test-key').catch(() => false),
      provider: 'redis'
    }
  }

  /**
   * Clear cache for operation
   */
  async clearCache(key: string): Promise<void> {
    try {
      await this.cacheProvider.delete(key)
      console.log(`🗑️ Cleared cache for key: ${key}`)
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }
}

// Singleton instance
export const fallbackManager = new FallbackManager()

// Utility function for easy fallback execution
export async function withFallback<T>(
  operationName: string,
  originalError: Error,
  config: Partial<FallbackConfig>,
  context?: Partial<FallbackContext>
): Promise<FallbackResult<T>> {
  const fallbackContext: FallbackContext = {
    operationName,
    originalError,
    attemptNumber: 1,
    ...context
  }

  return await fallbackManager.execute<T>(fallbackContext, config)
}
