/**
 * Comprehensive Test Suite for Enhanced Error Handling System
 * 
 * Tests all components of the error handling system including:
 * - Error classification and custom error types
 * - Retry mechanisms with different strategies
 * - Circuit breaker pattern behavior
 * - Fallback strategies and graceful degradation
 * - Error monitoring and alerting
 */

import { describe, test, expect, beforeEach, vi } from 'vitest'
import {
  AppError,
  ErrorCategory,
  ErrorSeverity,
  RetryStrategy,
  FallbackStrategy,
  ErrorFactory,
  isRetryableError,
  isNetworkError
} from '../types'

import { RetryManager, withRetry } from '../retry-manager'
import { CircuitBreaker, circuitBreakerRegistry } from '../circuit-breaker'
import { FallbackManager, fallbackManager, withFallback } from '../fallback-manager'
import { ErrorMonitor, errorMonitor, recordError } from '../error-monitor'
import { enhancedErrorHandling, executeWithErrorHandling, ErrorHandlingPresets } from '../index'

// Mock Redis for testing
vi.mock('redis', () => ({
  createClient: vi.fn(() => ({
    connect: vi.fn(),
    get: vi.fn(),
    set: vi.fn(),
    del: vi.fn(),
    exists: vi.fn(),
    ping: vi.fn(() => Promise.resolve('PONG')),
    on: vi.fn(),
    quit: vi.fn()
  }))
}))

describe('Error Handling System', () => {
  beforeEach(() => {
    // Reset circuit breakers and clear error history
    circuitBreakerRegistry.clear()
    vi.clearAllMocks()
  })

  describe('Error Types and Classification', () => {
    test('should create AppError with correct properties', () => {
      const error = new AppError(
        'Test error message',
        'TEST_ERROR',
        ErrorCategory.API,
        ErrorSeverity.HIGH,
        true,
        RetryStrategy.EXPONENTIAL,
        FallbackStrategy.CACHED_RESPONSE
      )

      expect(error.message).toBe('Test error message')
      expect(error.code).toBe('TEST_ERROR')
      expect(error.category).toBe(ErrorCategory.API)
      expect(error.severity).toBe(ErrorSeverity.HIGH)
      expect(error.retryable).toBe(true)
      expect(error.retryStrategy).toBe(RetryStrategy.EXPONENTIAL)
      expect(error.fallbackStrategy).toBe(FallbackStrategy.CACHED_RESPONSE)
    })

    test('should create errors from HTTP status codes', () => {
      const error404 = ErrorFactory.createFromHttpStatus(404, 'Not found')
      expect(error404.category).toBe(ErrorCategory.API)
      expect(error404.severity).toBe(ErrorSeverity.MEDIUM)

      const error500 = ErrorFactory.createFromHttpStatus(500, 'Internal error')
      expect(error500.category).toBe(ErrorCategory.INTERNAL)
      expect(error500.severity).toBe(ErrorSeverity.HIGH)

      const error429 = ErrorFactory.createFromHttpStatus(429, 'Rate limited')
      expect(error429.category).toBe(ErrorCategory.RATE_LIMIT)
      expect(error429.severity).toBe(ErrorSeverity.MEDIUM)
    })

    test('should correctly identify error types', () => {
      const networkError = ErrorFactory.createNetworkError('Connection failed')
      expect(isNetworkError(networkError)).toBe(true)
      expect(isRetryableError(networkError)).toBe(true)

      const validationError = new AppError(
        'Invalid input',
        'VALIDATION_ERROR',
        ErrorCategory.VALIDATION,
        ErrorSeverity.LOW,
        false
      )
      expect(isRetryableError(validationError)).toBe(false)
    })
  })

  describe('Retry Manager', () => {
    let retryManager: RetryManager

    beforeEach(() => {
      retryManager = new RetryManager()
    })

    test('should retry failed operations with exponential backoff', async () => {
      let attempts = 0
      const operation = vi.fn(async () => {
        attempts++
        if (attempts < 3) {
          throw new AppError('Temporary failure', 'TEMP_ERROR', ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, true)
        }
        return 'success'
      })

      const result = await retryManager.execute(operation, 'test-operation', {
        strategy: RetryStrategy.EXPONENTIAL,
        maxAttempts: 3,
        baseDelay: 100,
        maxDelay: 1000
      })

      expect(result.success).toBe(true)
      expect(result.result).toBe('success')
      expect(result.totalAttempts).toBe(3)
      expect(operation).toHaveBeenCalledTimes(3)
    })

    test('should fail after max attempts', async () => {
      const operation = vi.fn(async () => {
        throw new AppError('Persistent failure', 'PERSIST_ERROR', ErrorCategory.API, ErrorSeverity.HIGH, true)
      })

      const result = await retryManager.execute(operation, 'test-operation', {
        strategy: RetryStrategy.EXPONENTIAL,
        maxAttempts: 2,
        baseDelay: 50
      })

      expect(result.success).toBe(false)
      expect(result.totalAttempts).toBe(2)
      expect(operation).toHaveBeenCalledTimes(2)
    })

    test('should not retry non-retryable errors', async () => {
      const operation = vi.fn(async () => {
        throw new AppError('Validation error', 'VALIDATION_ERROR', ErrorCategory.VALIDATION, ErrorSeverity.LOW, false)
      })

      const result = await retryManager.execute(operation, 'test-operation', {
        strategy: RetryStrategy.EXPONENTIAL,
        maxAttempts: 3,
        baseDelay: 100
      })

      expect(result.success).toBe(false)
      expect(result.totalAttempts).toBe(1)
      expect(operation).toHaveBeenCalledTimes(1)
    })

    test('withRetry utility function should work correctly', async () => {
      let attempts = 0
      const operation = async () => {
        attempts++
        if (attempts < 2) {
          throw new AppError('Retry me', 'RETRY_ERROR', ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, true)
        }
        return 'success'
      }

      const result = await withRetry(operation, {
        strategy: RetryStrategy.FIXED,
        maxAttempts: 3,
        baseDelay: 50
      })

      expect(result).toBe('success')
      expect(attempts).toBe(2)
    })
  })

  describe('Circuit Breaker', () => {
    test('should open circuit after failure threshold', async () => {
      const circuitBreaker = new CircuitBreaker('test-breaker', {
        failureThreshold: 3,
        recoveryTimeout: 1000,
        enabled: true
      })

      const failingOperation = async () => {
        throw new Error('Service unavailable')
      }

      // Trigger failures to open circuit
      for (let i = 0; i < 3; i++) {
        try {
          await circuitBreaker.execute(failingOperation)
        } catch (error) {
          // Expected to fail
        }
      }

      expect(circuitBreaker.getState()).toBe('open')

      // Next call should fail fast
      const start = Date.now()
      try {
        await circuitBreaker.execute(failingOperation)
      } catch (error) {
        const elapsed = Date.now() - start
        expect(elapsed).toBeLessThan(100) // Should fail fast
      }
    })

    test('should transition to half-open after recovery timeout', async () => {
      const circuitBreaker = new CircuitBreaker('test-breaker-2', {
        failureThreshold: 2,
        recoveryTimeout: 100, // Short timeout for testing
        enabled: true
      })

      const failingOperation = async () => {
        throw new Error('Service unavailable')
      }

      // Open the circuit
      for (let i = 0; i < 2; i++) {
        try {
          await circuitBreaker.execute(failingOperation)
        } catch (error) {
          // Expected to fail
        }
      }

      expect(circuitBreaker.getState()).toBe('open')

      // Wait for recovery timeout
      await new Promise(resolve => setTimeout(resolve, 150))

      // Next call should transition to half-open
      try {
        await circuitBreaker.execute(failingOperation)
      } catch (error) {
        // Expected to fail, but state should be half-open
      }

      expect(circuitBreaker.getState()).toBe('half-open')
    })

    test('should close circuit on successful operation in half-open state', async () => {
      const circuitBreaker = new CircuitBreaker('test-breaker-3', {
        failureThreshold: 2,
        recoveryTimeout: 100,
        enabled: true
      })

      // Open the circuit
      for (let i = 0; i < 2; i++) {
        try {
          await circuitBreaker.execute(async () => { throw new Error('Fail') })
        } catch (error) {
          // Expected to fail
        }
      }

      // Wait for recovery
      await new Promise(resolve => setTimeout(resolve, 150))

      // Successful operation should close circuit
      const result = await circuitBreaker.execute(async () => 'success')
      expect(result).toBe('success')
      expect(circuitBreaker.getState()).toBe('closed')
    })
  })

  describe('Fallback Manager', () => {
    test('should execute mock data fallback', async () => {
      fallbackManager.registerMockDataProvider('test-operation', () => ({ mock: true, data: 'fallback' }))

      const result = await fallbackManager.execute({
        operationName: 'test-operation',
        originalError: new Error('Original failed'),
        attemptNumber: 3
      }, {
        strategy: FallbackStrategy.MOCK_DATA,
        enabled: true,
        timeout: 5000
      })

      expect(result.success).toBe(true)
      expect(result.data).toEqual({ mock: true, data: 'fallback' })
      expect(result.strategy).toBe(FallbackStrategy.MOCK_DATA)
    })

    test('should handle degraded functionality fallback', async () => {
      const result = await fallbackManager.execute({
        operationName: 'test-operation',
        originalError: new Error('Service down'),
        attemptNumber: 2
      }, {
        strategy: FallbackStrategy.DEGRADED_FUNCTIONALITY,
        enabled: true,
        timeout: 5000
      })

      expect(result.success).toBe(true)
      expect(result.data).toHaveProperty('degraded', true)
      expect(result.strategy).toBe(FallbackStrategy.DEGRADED_FUNCTIONALITY)
    })

    test('withFallback utility should work correctly', async () => {
      fallbackManager.registerMockDataProvider('util-test', () => 'fallback-data')

      const result = await withFallback(
        async () => { throw new Error('Operation failed') },
        'util-test',
        { strategy: FallbackStrategy.MOCK_DATA, enabled: true, timeout: 5000 }
      )

      expect(result).toBe('fallback-data')
    })
  })

  describe('Error Monitor', () => {
    test('should record and track errors', () => {
      const error = new AppError('Test error', 'TEST_ERROR', ErrorCategory.API, ErrorSeverity.HIGH)
      const errorId = recordError(error, { operationName: 'test-op', userId: 'user123' })

      expect(errorId).toBeDefined()
      
      const stats = errorMonitor.getStats()
      expect(stats.totalErrors).toBeGreaterThan(0)
      expect(stats.errorsByCategory[ErrorCategory.API]).toBeGreaterThan(0)
      expect(stats.errorsBySeverity[ErrorSeverity.HIGH]).toBeGreaterThan(0)
    })

    test('should provide health status', () => {
      const health = errorMonitor.getHealthStatus()
      expect(health).toHaveProperty('status')
      expect(health).toHaveProperty('errorRate')
      expect(health).toHaveProperty('criticalErrors')
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status)
    })
  })

  describe('Enhanced Error Handling Integration', () => {
    test('should execute operation with full error handling', async () => {
      let attempts = 0
      const operation = async () => {
        attempts++
        if (attempts < 2) {
          throw new AppError('Temporary failure', 'TEMP_ERROR', ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, true)
        }
        return { success: true, data: 'processed' }
      }

      const result = await executeWithErrorHandling(operation, 'integration-test', {
        retryConfig: {
          strategy: RetryStrategy.EXPONENTIAL,
          maxAttempts: 3,
          baseDelay: 50
        },
        fallbackConfig: {
          strategy: FallbackStrategy.NONE,
          enabled: false
        }
      })

      expect(result).toEqual({ success: true, data: 'processed' })
      expect(attempts).toBe(2)
    })

    test('should use fallback when retries fail', async () => {
      enhancedErrorHandling.registerMockDataProvider('fallback-test', () => ({ fallback: true }))

      const operation = async () => {
        throw new AppError('Always fails', 'ALWAYS_FAIL', ErrorCategory.EXTERNAL_SERVICE, ErrorSeverity.HIGH, true)
      }

      const result = await executeWithErrorHandling(operation, 'fallback-test', {
        retryConfig: {
          strategy: RetryStrategy.FIXED,
          maxAttempts: 2,
          baseDelay: 50
        },
        fallbackConfig: {
          strategy: FallbackStrategy.MOCK_DATA,
          enabled: true,
          timeout: 5000
        }
      })

      expect(result).toEqual({ fallback: true })
    })

    test('should use error handling presets correctly', async () => {
      const operation = async () => 'success'

      const result = await enhancedErrorHandling.executeWithErrorHandling(operation, {
        operationName: 'preset-test',
        ...ErrorHandlingPresets.externalAPI
      })

      expect(result.success).toBe(true)
      expect(result.data).toBe('success')
      expect(result.operationName).toBe('preset-test')
    })
  })

  describe('System Health Check', () => {
    test('should perform comprehensive health check', async () => {
      const health = await enhancedErrorHandling.healthCheck()
      
      expect(health).toHaveProperty('status')
      expect(health).toHaveProperty('components')
      expect(health).toHaveProperty('details')
      expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status)
      
      expect(health.components).toHaveProperty('errorMonitoring')
      expect(health.components).toHaveProperty('circuitBreakers')
      expect(health.components).toHaveProperty('fallbackCache')
      expect(health.components).toHaveProperty('retryManager')
    })
  })
})
