const puppeteer = require('puppeteer');

describe('Canvas Implementation Tests', () => {
  let browser;
  let page;
  const baseUrl = 'http://localhost:3001';

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: false,
      slowMo: 50,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    page.setDefaultTimeout(10000);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  test('Canvas Test Page Loads Successfully', async () => {
    console.log('🧪 Testing canvas test page loading...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });

    // Check page title
    const title = await page.title();
    expect(title).toContain('I2D Convert');
    
    // Check main heading
    const heading = await page.$eval('h1', el => el.textContent);
    expect(heading).toBe('Canvas Test Environment (Standalone)');
    
    console.log('✅ Canvas test page loaded successfully');
  });

  test('Control Buttons Are Present', async () => {
    console.log('🧪 Testing control buttons...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Get all button texts
    const buttons = await page.$$('button');
    const buttonTexts = await Promise.all(
      buttons.map(btn => page.evaluate(el => el.textContent.trim(), btn))
    );
    
    // Check for required buttons
    expect(buttonTexts.some(text => text.includes('Load Mock Data'))).toBe(true);
    expect(buttonTexts.some(text => text.includes('Stress Test'))).toBe(true);
    expect(buttonTexts.some(text => text.includes('Clear Canvas'))).toBe(true);
    expect(buttonTexts.some(text => text.includes('Properties Panel'))).toBe(true);
    
    console.log('✅ All control buttons found:', buttonTexts.filter(text => 
      text.includes('Load Mock Data') || 
      text.includes('Stress Test') || 
      text.includes('Clear Canvas') || 
      text.includes('Properties Panel')
    ));
  });

  test('Instructions Overlay Shows When Canvas Is Empty', async () => {
    console.log('🧪 Testing instructions overlay...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Check for instructions overlay
    const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center.bg-black');
    expect(overlay).toBeTruthy();
    
    // Check instructions text
    const instructionsHeading = await page.$eval('h3', el => el.textContent);
    expect(instructionsHeading).toBe('Canvas Test Environment');
    
    console.log('✅ Instructions overlay displayed correctly');
  });

  test('Mock Data Loading Works', async () => {
    console.log('🧪 Testing mock data loading...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Find and click Load Mock Data button
    const buttons = await page.$$('button');
    let mockDataButton = null;
    
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Load Mock Data')) {
        mockDataButton = button;
        break;
      }
    }
    
    expect(mockDataButton).toBeTruthy();
    await mockDataButton.click();
    
    // Wait for update
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    // Check if instructions overlay is hidden
    const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center.bg-black');
    expect(overlay).toBeFalsy();
    
    // Check component count in status bar
    const statusBar = await page.$('.bg-gray-50.border-t');
    const statusText = await page.evaluate(el => el.textContent, statusBar);
    expect(statusText).toContain('Components: 10');
    
    console.log('✅ Mock data loaded successfully');
  });

  test('Canvas Element Is Present After Loading Data', async () => {
    console.log('🧪 Testing canvas element presence...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Load mock data
    const buttons = await page.$$('button');
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Load Mock Data')) {
        await button.click();
        break;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Check for canvas element
    const canvas = await page.$('canvas');
    expect(canvas).toBeTruthy();
    
    // Check canvas dimensions
    const canvasRect = await canvas.boundingBox();
    expect(canvasRect.width).toBeGreaterThan(0);
    expect(canvasRect.height).toBeGreaterThan(0);
    
    console.log('✅ Canvas element present with dimensions:', canvasRect);
  });

  test('Properties Panel Toggle Works', async () => {
    console.log('🧪 Testing properties panel toggle...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Load mock data first
    const buttons = await page.$$('button');
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Load Mock Data')) {
        await button.click();
        break;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if properties panel is initially visible
    let propertiesPanel = await page.$('.w-80.bg-white.border-l');
    expect(propertiesPanel).toBeTruthy();
    
    // Find and click properties panel toggle
    const allButtons = await page.$$('button');
    for (const button of allButtons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Properties Panel')) {
        await button.click();
        break;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 500));

    // Check if properties panel is hidden
    propertiesPanel = await page.$('.w-80.bg-white.border-l');
    expect(propertiesPanel).toBeFalsy();
    
    console.log('✅ Properties panel toggle works correctly');
  });

  test('Stress Test Loading Works', async () => {
    console.log('🧪 Testing stress test loading...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Find and click Stress Test button
    const buttons = await page.$$('button');
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Stress Test')) {
        await button.click();
        break;
      }
    }
    
    // Wait longer for stress test
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Check component count
    const statusBar = await page.$('.bg-gray-50.border-t');
    const statusText = await page.evaluate(el => el.textContent, statusBar);
    expect(statusText).toContain('Components: 50');
    
    // Verify canvas still works
    const canvas = await page.$('canvas');
    expect(canvas).toBeTruthy();
    
    console.log('✅ Stress test loaded successfully with 50 components');
  });

  test('Clear Canvas Works', async () => {
    console.log('🧪 Testing clear canvas functionality...');
    
    await page.goto(`${baseUrl}/canvas-test-standalone`, { waitUntil: 'networkidle0' });
    
    // Load mock data first
    const buttons = await page.$$('button');
    for (const button of buttons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Load Mock Data')) {
        await button.click();
        break;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify data is loaded
    let statusBar = await page.$('.bg-gray-50.border-t');
    let statusText = await page.evaluate(el => el.textContent, statusBar);
    expect(statusText).toContain('Components: 10');
    
    // Clear canvas
    const allButtons = await page.$$('button');
    for (const button of allButtons) {
      const text = await page.evaluate(el => el.textContent, button);
      if (text.includes('Clear Canvas')) {
        await button.click();
        break;
      }
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Check if components are cleared
    statusBar = await page.$('.bg-gray-50.border-t');
    statusText = await page.evaluate(el => el.textContent, statusBar);
    expect(statusText).toContain('Components: 0');
    
    // Check if instructions overlay is back
    const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center.bg-black');
    expect(overlay).toBeTruthy();
    
    console.log('✅ Clear canvas works correctly');
  });
});
