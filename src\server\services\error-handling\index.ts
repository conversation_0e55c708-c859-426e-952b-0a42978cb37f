/**
 * Enhanced Error Handling and Retry Logic System
 * 
 * Main entry point that integrates all error handling components:
 * - Error classification and custom error types
 * - Advanced retry mechanisms with multiple strategies
 * - Circuit breaker pattern for service protection
 * - Fallback strategies for graceful degradation
 * - Comprehensive error monitoring and alerting
 */

import { 
  AppError,
  ErrorCategory,
  ErrorSeverity,
  RetryStrategy,
  FallbackStrategy,
  ErrorHandlingConfig,
  ErrorFactory,
  isRetryableError,
  isNetworkError,
  isAPIError,
  isRateLimitError,
  isTimeoutError
} from './types'

import { RetryManager, withRetry, RetryResult } from './retry-manager'
import { CircuitBreaker, circuitBreakerRegistry } from './circuit-breaker'
import { FallbackManager, fallbackManager, withFallback, FallbackResult } from './fallback-manager'
import { ErrorMonitor, errorMonitor, recordError, getErrorStats, getHealthStatus } from './error-monitor'

// Enhanced operation execution with full error handling
export interface EnhancedOperationConfig {
  operationName: string
  retryConfig?: Partial<import('./types').RetryConfig>
  circuitBreakerConfig?: Partial<import('./types').CircuitBreakerConfig>
  fallbackConfig?: Partial<import('./types').FallbackConfig>
  timeout?: number
  userId?: string
  metadata?: Record<string, any>
}

// Enhanced operation result
export interface EnhancedOperationResult<T> {
  success: boolean
  data?: T
  error?: Error
  retryResult?: RetryResult<T>
  fallbackResult?: FallbackResult<T>
  circuitBreakerUsed: boolean
  totalElapsed: number
  operationName: string
}

/**
 * Enhanced Error Handling Service
 * 
 * Provides a unified interface for executing operations with comprehensive
 * error handling, retry logic, circuit breakers, and fallback strategies.
 */
export class EnhancedErrorHandlingService {
  private retryManager: RetryManager
  private fallbackManager: FallbackManager
  private errorMonitor: ErrorMonitor

  constructor() {
    this.retryManager = new RetryManager()
    this.fallbackManager = fallbackManager
    this.errorMonitor = errorMonitor
    
    console.log('🛡️ Enhanced Error Handling Service initialized')
  }

  /**
   * Execute an operation with full error handling capabilities
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    config: EnhancedOperationConfig
  ): Promise<EnhancedOperationResult<T>> {
    const startTime = Date.now()
    let circuitBreakerUsed = false
    let retryResult: RetryResult<T> | undefined
    let fallbackResult: FallbackResult<T> | undefined

    try {
      // Get or create circuit breaker for this operation
      const circuitBreaker = circuitBreakerRegistry.getOrCreate(
        config.operationName,
        config.circuitBreakerConfig
      )

      // Set up circuit breaker event monitoring
      circuitBreaker.onEvent((event) => {
        this.errorMonitor.recordCircuitBreakerEvent(event, config.operationName)
      })

      // Execute operation with circuit breaker and retry logic
      const operationWithCircuitBreaker = async () => {
        circuitBreakerUsed = true
        return await circuitBreaker.execute(operation, config.timeout)
      }

      // Execute with retry logic
      retryResult = await this.retryManager.execute(
        operationWithCircuitBreaker,
        config.operationName,
        config.retryConfig
      )

      // Record retry result for monitoring
      this.errorMonitor.recordRetryResult(retryResult, config.operationName)

      if (retryResult.success) {
        return {
          success: true,
          data: retryResult.result,
          retryResult,
          circuitBreakerUsed,
          totalElapsed: Date.now() - startTime,
          operationName: config.operationName
        }
      } else {
        // Operation failed after retries, try fallback
        if (config.fallbackConfig && config.fallbackConfig.strategy !== FallbackStrategy.NONE) {
          fallbackResult = await this.fallbackManager.execute({
            operationName: config.operationName,
            originalError: retryResult.error!,
            attemptNumber: retryResult.totalAttempts,
            userId: config.userId,
            metadata: config.metadata
          }, config.fallbackConfig)

          // Record fallback result for monitoring
          this.errorMonitor.recordFallbackResult(fallbackResult, config.operationName)

          if (fallbackResult.success) {
            return {
              success: true,
              data: fallbackResult.data,
              retryResult,
              fallbackResult,
              circuitBreakerUsed,
              totalElapsed: Date.now() - startTime,
              operationName: config.operationName
            }
          }
        }

        // Both retry and fallback failed
        const finalError = fallbackResult?.originalError || retryResult.error!
        
        // Record the final error
        recordError(finalError, {
          operationName: config.operationName,
          userId: config.userId,
          metadata: {
            ...config.metadata,
            retryAttempts: retryResult.totalAttempts,
            fallbackUsed: !!fallbackResult,
            circuitBreakerUsed
          }
        })

        return {
          success: false,
          error: finalError,
          retryResult,
          fallbackResult,
          circuitBreakerUsed,
          totalElapsed: Date.now() - startTime,
          operationName: config.operationName
        }
      }

    } catch (error) {
      // Unexpected error in error handling system
      const enhancedError = error instanceof AppError ? 
        error : 
        ErrorFactory.createFromHttpStatus(500, (error as Error).message, {
          operationName: config.operationName,
          userId: config.userId,
          metadata: config.metadata
        })

      recordError(enhancedError, {
        operationName: config.operationName,
        userId: config.userId,
        metadata: config.metadata
      })

      return {
        success: false,
        error: enhancedError,
        circuitBreakerUsed,
        totalElapsed: Date.now() - startTime,
        operationName: config.operationName
      }
    }
  }

  /**
   * Register mock data provider for fallback
   */
  registerMockDataProvider<T>(operationName: string, provider: (context: any) => T | Promise<T>): void {
    this.fallbackManager.registerMockDataProvider(operationName, provider)
  }

  /**
   * Register alternative service for fallback
   */
  registerAlternativeService(operationName: string, serviceUrl: string): void {
    this.fallbackManager.registerAlternativeService(operationName, serviceUrl)
  }

  /**
   * Cache successful response for future fallback use
   */
  async cacheResponse<T>(key: string, data: T, ttl?: number): Promise<void> {
    await this.fallbackManager.cacheResponse(key, data, ttl)
  }

  /**
   * Get comprehensive error statistics
   */
  getErrorStatistics() {
    return {
      errors: getErrorStats(),
      health: getHealthStatus(),
      circuitBreakers: this.getCircuitBreakerStatus()
    }
  }

  /**
   * Get circuit breaker status for all registered breakers
   */
  getCircuitBreakerStatus() {
    const breakers = circuitBreakerRegistry.getAll()
    const status: Record<string, any> = {}

    breakers.forEach((breaker, name) => {
      status[name] = {
        state: breaker.getState(),
        metrics: breaker.getMetrics(),
        healthy: breaker.isHealthy()
      }
    })

    return status
  }

  /**
   * Health check for the entire error handling system
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy'
    components: Record<string, boolean>
    details: any
  }> {
    const health = getHealthStatus()
    const circuitBreakerHealth = circuitBreakerRegistry.getHealthStatus()
    const cacheStats = await this.fallbackManager.getCacheStats()

    const components = {
      errorMonitoring: health.status === 'healthy',
      circuitBreakers: Object.values(circuitBreakerHealth).every(healthy => healthy),
      fallbackCache: cacheStats.available,
      retryManager: true // Always available
    }

    const allHealthy = Object.values(components).every(healthy => healthy)
    const mostlyHealthy = Object.values(components).filter(healthy => healthy).length >= 3

    return {
      status: allHealthy ? 'healthy' : mostlyHealthy ? 'degraded' : 'unhealthy',
      components,
      details: {
        errorStats: health,
        circuitBreakers: circuitBreakerHealth,
        cache: cacheStats
      }
    }
  }
}

// Create singleton instance
export const enhancedErrorHandling = new EnhancedErrorHandlingService()

// Convenience function for simple operations
export async function executeWithErrorHandling<T>(
  operation: () => Promise<T>,
  operationName: string,
  config: Partial<EnhancedOperationConfig> = {}
): Promise<T> {
  const result = await enhancedErrorHandling.executeWithErrorHandling(operation, {
    operationName,
    ...config
  })

  if (result.success) {
    return result.data!
  } else {
    throw result.error
  }
}

// Export all types and utilities
export {
  // Types
  AppError,
  ErrorCategory,
  ErrorSeverity,
  RetryStrategy,
  FallbackStrategy,
  ErrorHandlingConfig,
  
  // Error factory
  ErrorFactory,
  
  // Type guards
  isRetryableError,
  isNetworkError,
  isAPIError,
  isRateLimitError,
  isTimeoutError,
  
  // Individual components
  RetryManager,
  CircuitBreaker,
  FallbackManager,
  ErrorMonitor,
  
  // Utility functions
  withRetry,
  withFallback,
  recordError,
  getErrorStats,
  getHealthStatus,
  
  // Registries
  circuitBreakerRegistry,
  fallbackManager,
  errorMonitor
}

// Default configurations for common scenarios
export const ErrorHandlingPresets = {
  // For external API calls
  externalAPI: {
    retryConfig: {
      strategy: RetryStrategy.EXPONENTIAL,
      maxAttempts: 3,
      baseDelay: 2000,
      maxDelay: 15000,
      jitter: true
    },
    circuitBreakerConfig: {
      failureThreshold: 5,
      recoveryTimeout: 60000,
      enabled: true
    },
    fallbackConfig: {
      strategy: FallbackStrategy.CACHED_RESPONSE,
      enabled: true,
      timeout: 5000
    }
  },

  // For critical operations
  critical: {
    retryConfig: {
      strategy: RetryStrategy.EXPONENTIAL,
      maxAttempts: 5,
      baseDelay: 1000,
      maxDelay: 30000,
      jitter: true
    },
    circuitBreakerConfig: {
      failureThreshold: 3,
      recoveryTimeout: 30000,
      enabled: true
    },
    fallbackConfig: {
      strategy: FallbackStrategy.MANUAL_INTERVENTION,
      enabled: true,
      timeout: 10000
    }
  },

  // For user-facing operations
  userFacing: {
    retryConfig: {
      strategy: RetryStrategy.LINEAR,
      maxAttempts: 2,
      baseDelay: 1000,
      maxDelay: 5000,
      jitter: false
    },
    circuitBreakerConfig: {
      failureThreshold: 10,
      recoveryTimeout: 120000,
      enabled: true
    },
    fallbackConfig: {
      strategy: FallbackStrategy.DEGRADED_FUNCTIONALITY,
      enabled: true,
      timeout: 3000
    }
  }
}
