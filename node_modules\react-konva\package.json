{"license": "MIT", "name": "react-konva", "description": "React binding to canvas element via Konva framework", "version": "18.2.12", "keywords": ["react", "canvas", "jsx", "konva"], "bugs": "https://github.com/konvajs/react-konva/issues", "main": "lib/ReactKonva.js", "module": "es/ReactKonva.js", "repository": {"type": "git", "url": "**************:konvajs/react-konva.git"}, "dependencies": {"@types/react-reconciler": "^0.28.2", "its-fine": "^1.1.1", "react-reconciler": "~0.29.0", "scheduler": "^0.23.0"}, "targets": {"none": {}}, "funding": [{"type": "patreon", "url": "https://www.patreon.com/lavrton"}, {"type": "opencollective", "url": "https://opencollective.com/konva"}, {"type": "github", "url": "https://github.com/sponsors/lavrton"}], "peerDependencies": {"konva": "^8.0.1 || ^7.2.5 || ^9.0.0", "react": ">=18.0.0", "react-dom": ">=18.0.0"}, "devDependencies": {"@types/chai": "^4.3.5", "@types/mocha": "^10.0.1", "@types/react": "18.2.8", "chai": "4.3.7", "konva": "^9.1.0", "mocha-headless-chrome": "^4.0.0", "parcel": "^2.9.1", "process": "^0.11.10", "react": "^18.2.0", "react-dom": "^18.2.0", "sinon": "^15.1.0", "timers-browserify": "^2.0.12", "typescript": "^5.1.3", "use-image": "^1.1.0", "util": "^0.12.5"}, "scripts": {"build": "tsc -outDir ./es &&  tsc -module commonjs -outDir ./lib && cp ./ReactKonvaCore.d.ts ./lib && cp ./ReactKonvaCore.d.ts ./es", "test:typings": "tsc --noEmit", "preversion": "", "version": "npm run build", "postversion": "", "test": "npm run test:build && mocha-headless-chrome -f ./test-build/unit-tests.html -a disable-web-security && npm run test:typings", "test:build": "parcel build ./test/unit-tests.html --dist-dir test-build --target none --public-url ./ --no-source-maps", "test:watch": "rm -rf ./parcel-cache && parcel serve ./test/unit-tests.html"}, "typings": "react-konva.d.ts", "files": ["README.md", "lib", "es", "react-konva.d.ts", "ReactKonvaCore.d.ts"]}