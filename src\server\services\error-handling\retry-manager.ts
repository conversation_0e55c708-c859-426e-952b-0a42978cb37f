/**
 * Advanced Retry Manager
 * 
 * Provides sophisticated retry logic with multiple backoff strategies,
 * jitter, and configurable retry policies for different error types.
 */

import { 
  RetryConfig, 
  RetryStrategy, 
  AppError, 
  isRetryableError,
  ErrorSeverity 
} from './types'

// Retry attempt information
export interface RetryAttempt {
  attemptNumber: number
  delay: number
  error: Error
  timestamp: Date
  totalElapsed: number
}

// Retry result
export interface RetryResult<T> {
  success: boolean
  result?: T
  error?: Error
  attempts: RetryAttempt[]
  totalAttempts: number
  totalElapsed: number
}

// Retry context for tracking state
interface RetryContext {
  attempts: RetryAttempt[]
  startTime: Date
  lastAttemptTime: Date
}

export class RetryManager {
  private defaultConfig: RetryConfig = {
    strategy: RetryStrategy.EXPONENTIAL,
    maxAttempts: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    jitter: true,
    backoffMultiplier: 2,
    retryableErrors: [],
    nonRetryableErrors: []
  }

  constructor(private config: Partial<RetryConfig> = {}) {
    this.config = { ...this.defaultConfig, ...config }
  }

  /**
   * Execute a function with retry logic
   */
  async execute<T>(
    operation: () => Promise<T>,
    operationName: string = 'operation',
    customConfig?: Partial<RetryConfig>
  ): Promise<RetryResult<T>> {
    const config = { ...this.config, ...customConfig }
    const context: RetryContext = {
      attempts: [],
      startTime: new Date(),
      lastAttemptTime: new Date()
    }

    console.log(`🔄 Starting retry operation: ${operationName}`)

    for (let attempt = 1; attempt <= config.maxAttempts; attempt++) {
      try {
        const result = await operation()
        
        // Success - log and return
        const totalElapsed = Date.now() - context.startTime.getTime()
        console.log(`✅ Operation ${operationName} succeeded on attempt ${attempt} (${totalElapsed}ms)`)
        
        return {
          success: true,
          result,
          attempts: context.attempts,
          totalAttempts: attempt,
          totalElapsed
        }

      } catch (error) {
        const now = new Date()
        const totalElapsed = now.getTime() - context.startTime.getTime()
        
        // Record attempt
        const retryAttempt: RetryAttempt = {
          attemptNumber: attempt,
          delay: 0, // Will be set below if retrying
          error: error as Error,
          timestamp: now,
          totalElapsed
        }

        context.attempts.push(retryAttempt)
        context.lastAttemptTime = now

        // Check if we should retry
        if (!this.shouldRetry(error, attempt, config)) {
          console.log(`❌ Operation ${operationName} failed permanently after ${attempt} attempts (${totalElapsed}ms)`)
          return {
            success: false,
            error: error as Error,
            attempts: context.attempts,
            totalAttempts: attempt,
            totalElapsed
          }
        }

        // Calculate delay for next attempt
        if (attempt < config.maxAttempts) {
          const delay = this.calculateDelay(attempt, config, error)
          retryAttempt.delay = delay

          console.log(`⏳ Operation ${operationName} failed on attempt ${attempt}, retrying in ${delay}ms. Error: ${(error as Error).message}`)
          
          // Wait before next attempt
          await this.sleep(delay)
        }
      }
    }

    // All attempts exhausted
    const totalElapsed = Date.now() - context.startTime.getTime()
    const lastError = context.attempts[context.attempts.length - 1]?.error

    console.log(`❌ Operation ${operationName} failed after ${config.maxAttempts} attempts (${totalElapsed}ms)`)
    
    return {
      success: false,
      error: lastError,
      attempts: context.attempts,
      totalAttempts: config.maxAttempts,
      totalElapsed
    }
  }

  /**
   * Determine if an error should be retried
   */
  private shouldRetry(error: any, attempt: number, config: RetryConfig): boolean {
    // Don't retry if we've reached max attempts
    if (attempt >= config.maxAttempts) {
      return false
    }

    // Check non-retryable errors first
    if (config.nonRetryableErrors.length > 0) {
      const errorCode = error instanceof AppError ? error.code : error.name
      if (config.nonRetryableErrors.includes(errorCode)) {
        return false
      }
    }

    // Check if error is explicitly retryable
    if (config.retryableErrors.length > 0) {
      const errorCode = error instanceof AppError ? error.code : error.name
      return config.retryableErrors.includes(errorCode)
    }

    // Use error's retryable property if available
    if (isRetryableError(error)) {
      return true
    }

    // Default retry logic for common error types
    if (error instanceof AppError) {
      return error.retryable
    }

    // Network-related errors are generally retryable
    if (error.code === 'ECONNRESET' || 
        error.code === 'ENOTFOUND' || 
        error.code === 'ECONNREFUSED' ||
        error.code === 'ETIMEDOUT') {
      return true
    }

    // HTTP status codes that are retryable
    if (error.status || error.statusCode) {
      const status = error.status || error.statusCode
      return status >= 500 || status === 429 || status === 408
    }

    return false
  }

  /**
   * Calculate delay for next retry attempt
   */
  private calculateDelay(attempt: number, config: RetryConfig, error?: any): number {
    let delay: number

    switch (config.strategy) {
      case RetryStrategy.FIXED:
        delay = config.baseDelay
        break

      case RetryStrategy.LINEAR:
        delay = config.baseDelay * attempt
        break

      case RetryStrategy.EXPONENTIAL:
        delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
        break

      case RetryStrategy.CUSTOM:
        // For rate limit errors, use the retry-after header if available
        if (error && (error.retryAfter || error.headers?.['retry-after'])) {
          const retryAfter = error.retryAfter || parseInt(error.headers['retry-after'])
          delay = retryAfter * 1000 // Convert to milliseconds
        } else {
          delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1)
        }
        break

      default:
        delay = config.baseDelay
    }

    // Apply maximum delay limit
    delay = Math.min(delay, config.maxDelay)

    // Add jitter to prevent thundering herd
    if (config.jitter) {
      const jitterAmount = delay * 0.1 // 10% jitter
      const jitter = (Math.random() - 0.5) * 2 * jitterAmount
      delay = Math.max(0, delay + jitter)
    }

    return Math.round(delay)
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Create a retry configuration for specific error types
   */
  static createConfigForErrorType(errorType: string): Partial<RetryConfig> {
    switch (errorType) {
      case 'network':
        return {
          strategy: RetryStrategy.EXPONENTIAL,
          maxAttempts: 5,
          baseDelay: 1000,
          maxDelay: 30000,
          backoffMultiplier: 2,
          jitter: true
        }

      case 'api':
        return {
          strategy: RetryStrategy.EXPONENTIAL,
          maxAttempts: 3,
          baseDelay: 2000,
          maxDelay: 15000,
          backoffMultiplier: 1.5,
          jitter: true
        }

      case 'rate_limit':
        return {
          strategy: RetryStrategy.CUSTOM,
          maxAttempts: 5,
          baseDelay: 5000,
          maxDelay: 60000,
          backoffMultiplier: 1.5,
          jitter: false
        }

      case 'timeout':
        return {
          strategy: RetryStrategy.LINEAR,
          maxAttempts: 3,
          baseDelay: 3000,
          maxDelay: 10000,
          backoffMultiplier: 1,
          jitter: true
        }

      default:
        return {
          strategy: RetryStrategy.EXPONENTIAL,
          maxAttempts: 3,
          baseDelay: 1000,
          maxDelay: 10000,
          backoffMultiplier: 2,
          jitter: true
        }
    }
  }

  /**
   * Get retry statistics
   */
  getStats(result: RetryResult<any>): {
    successRate: number
    averageDelay: number
    totalTime: number
    errorTypes: Record<string, number>
  } {
    const errorTypes: Record<string, number> = {}
    let totalDelay = 0

    result.attempts.forEach(attempt => {
      const errorType = attempt.error instanceof AppError ? 
        attempt.error.category : 
        attempt.error.name
      
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1
      totalDelay += attempt.delay
    })

    return {
      successRate: result.success ? 1 : 0,
      averageDelay: result.attempts.length > 0 ? totalDelay / result.attempts.length : 0,
      totalTime: result.totalElapsed,
      errorTypes
    }
  }
}

// Singleton instance for global use
export const retryManager = new RetryManager()

// Utility function for simple retry operations
export async function withRetry<T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>,
  operationName?: string
): Promise<T> {
  const manager = new RetryManager(config)
  const result = await manager.execute(operation, operationName)
  
  if (result.success) {
    return result.result!
  } else {
    throw result.error
  }
}
