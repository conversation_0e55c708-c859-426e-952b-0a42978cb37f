/**
 * Test setup for Error Handling System
 *
 * Configures test environment and mocks for error handling tests
 */

import { vi, beforeEach, afterEach } from 'vitest'

// Mock Redis client
vi.mock('redis', () => ({
  createClient: vi.fn(() => ({
    connect: vi.fn().mockResolvedValue(undefined),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    setEx: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    ping: vi.fn().mockResolvedValue('PONG'),
    on: vi.fn(),
    off: vi.fn(),
    quit: vi.fn().mockResolvedValue('OK'),
    isReady: true,
    isOpen: true
  }))
}))

// Mock console methods to reduce test noise
const originalConsole = { ...console }

beforeEach(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = vi.fn()
  console.warn = vi.fn()
  console.error = vi.fn()
})

afterEach(() => {
  // Restore console for debugging if needed
  if (process.env.DEBUG_TESTS) {
    console.log = originalConsole.log
    console.warn = originalConsole.warn
    console.error = originalConsole.error
  }
})

// Mock timers for testing retry delays and circuit breaker timeouts
beforeEach(() => {
  vi.useFakeTimers()
})

afterEach(() => {
  vi.useRealTimers()
  vi.clearAllTimers()
})

export {};
