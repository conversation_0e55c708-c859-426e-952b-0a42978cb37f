import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useAuthStore } from './store/authStore';
import ProtectedRoute from './components/ProtectedRoute';
import AppLayout from './components/Layout/AppLayout';
import NotificationContainer from './components/Notifications/NotificationContainer';

// Auth Pages
import Login from './pages/Login';
import Register from './pages/Register';
import ForgotPassword from './pages/ForgotPassword';
import ResetPassword from './pages/ResetPassword';

// App Pages
import Dashboard from './pages/Dashboard';
import Images from './pages/Images';
import Canvas from './pages/Canvas';
import CanvasTest from './pages/CanvasTest';
import CanvasTestStandalone from './pages/CanvasTestStandalone';
import Projects from './pages/Projects';
import Processing from './pages/Processing';
import Settings from './pages/Settings';
import WebSocketTest from './pages/WebSocketTest';
import SimpleWebSocketTest from './pages/SimpleWebSocketTest';

function App() {
  const { token, refreshProfile } = useAuthStore();

  useEffect(() => {
    // Refresh user profile on app load if we have a token
    if (token) {
      refreshProfile();
    }
  }, [token, refreshProfile]);

  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Routes>
          {/* Public Routes - redirect to dashboard if authenticated */}
          <Route
            path="/login"
            element={
              <ProtectedRoute requireAuth={false} redirectTo="/dashboard">
                <Login />
              </ProtectedRoute>
            }
          />
          <Route
            path="/register"
            element={
              <ProtectedRoute requireAuth={false} redirectTo="/dashboard">
                <Register />
              </ProtectedRoute>
            }
          />
          <Route
            path="/forgot-password"
            element={
              <ProtectedRoute requireAuth={false} redirectTo="/dashboard">
                <ForgotPassword />
              </ProtectedRoute>
            }
          />
          <Route
            path="/reset-password"
            element={
              <ProtectedRoute requireAuth={false} redirectTo="/dashboard">
                <ResetPassword />
              </ProtectedRoute>
            }
          />

          {/* Protected Routes - require authentication */}
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Dashboard />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/images"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Images />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/canvas/:imageId"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Canvas />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/canvas-test"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <CanvasTest />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/projects"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Projects />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/processing"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Processing />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/settings"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <Settings />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/websocket-test"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <WebSocketTest />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/simple-websocket-test"
            element={
              <ProtectedRoute>
                <AppLayout>
                  <SimpleWebSocketTest />
                </AppLayout>
              </ProtectedRoute>
            }
          />
          <Route
            path="/canvas-test-standalone"
            element={<CanvasTestStandalone />}
          />

          {/* Default redirects */}
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>

        {/* Global Notification Container */}
        <NotificationContainer />
      </div>
    </Router>
  );
}

export default App;
