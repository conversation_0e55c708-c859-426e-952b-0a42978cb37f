import OpenAI from 'openai'
import { config } from '../config'
import {
  enhancedErrorHandling,
  ErrorHandlingPresets,
  AppError,
  ErrorFactory,
  ErrorCategory,
  ErrorSeverity,
  FallbackStrategy,
  RetryStrategy
} from './error-handling'

interface ComponentDetection {
  id: string
  type: string
  label: string
  confidence: number
  boundingBox: {
    x: number
    y: number
    width: number
    height: number
  }
  properties: Record<string, any>
}

interface HierarchicalRelationship {
  parentId: string | null
  childIds: string[]
  depth: number
  containerType?: string
}

interface VisionAnalysisResult {
  components: ComponentDetection[]
  hierarchy: Record<string, HierarchicalRelationship>
  metadata: {
    imageWidth: number
    imageHeight: number
    processingTime: number
    modelUsed: string
    confidence: number
  }
}

interface ProcessingOptions {
  mode: 'sketch' | 'cartoon' | 'anime' | 'ui' | 'auto'
  includeHierarchy: boolean
  minConfidence: number
  maxComponents: number
}

class OpenAIServiceClass {
  private client: OpenAI | null = null
  private isInitialized: boolean = false
  private rateLimitTracker: Map<string, number[]> = new Map()

  async initialize(): Promise<void> {
    try {
      // Check if we should run in mock mode
      const isMockMode = !config.openai.apiKey ||
                        config.openai.apiKey === 'test' ||
                        config.openai.apiKey.length < 20

      if (isMockMode) {
        console.log('✓ OpenAI running in mock mode (no valid API key)')
        this.isInitialized = true
        this.setupMockDataProvider()
        return
      }

      // Initialize OpenAI client
      this.client = new OpenAI({
        apiKey: config.openai.apiKey,
      })

      // Test the connection with enhanced error handling
      try {
        const result = await enhancedErrorHandling.executeWithErrorHandling(
          () => this.testConnection(),
          {
            operationName: 'openai-connection-test',
            ...ErrorHandlingPresets.externalAPI,
            fallbackConfig: {
              strategy: FallbackStrategy.MOCK_DATA,
              enabled: true,
              timeout: 5000
            }
          }
        )

        if (result.success) {
          this.isInitialized = true
          console.log('✓ OpenAI service initialized successfully with real API')
        } else {
          console.warn('⚠️ OpenAI API connection failed, falling back to mock mode:', result.error)
          this.client = null // Fall back to mock mode
          this.isInitialized = true
        }
      } catch (connectionError) {
        console.warn('⚠️ OpenAI API connection failed, falling back to mock mode:', connectionError)
        this.client = null // Fall back to mock mode
        this.isInitialized = true
      }

      // Setup mock data provider for fallback scenarios
      this.setupMockDataProvider()

    } catch (error) {
      console.error('✗ Failed to initialize OpenAI service:', error)
      // Don't throw error, fall back to mock mode
      this.client = null
      this.isInitialized = true
      this.setupMockDataProvider()
      console.log('✓ OpenAI service initialized in mock mode due to error')
    }
  }

  private async testConnection(): Promise<void> {
    if (!this.client) {
      throw new Error('OpenAI client not initialized')
    }

    try {
      // Test with a simple completion to verify API key
      await this.client.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [{ role: 'user', content: 'Test connection' }],
        max_tokens: 1,
      })
    } catch (error) {
      throw new Error(`OpenAI API connection test failed: ${error}`)
    }
  }

  private checkRateLimit(userId: string): boolean {
    const now = Date.now()
    const windowMs = 60 * 1000 // 1 minute window
    const maxRequests = 10 // Max 10 requests per minute per user

    if (!this.rateLimitTracker.has(userId)) {
      this.rateLimitTracker.set(userId, [])
    }

    const userRequests = this.rateLimitTracker.get(userId)!
    
    // Remove requests older than the window
    const validRequests = userRequests.filter(timestamp => now - timestamp < windowMs)
    
    if (validRequests.length >= maxRequests) {
      return false // Rate limit exceeded
    }

    // Add current request
    validRequests.push(now)
    this.rateLimitTracker.set(userId, validRequests)
    
    return true
  }

  private generatePrompt(mode: string, includeHierarchy: boolean): string {
    const basePrompt = `You are an expert UI/UX analyzer. Analyze this image and detect UI components with their bounding boxes.

Return a JSON response with the following structure:
{
  "components": [
    {
      "id": "unique_id",
      "type": "button|input|text|image|container|navigation|card|modal|dropdown|checkbox|radio|slider|toggle|icon|divider|spacer",
      "label": "descriptive_label",
      "confidence": 0.95,
      "boundingBox": {
        "x": 0.1,
        "y": 0.2,
        "width": 0.3,
        "height": 0.1
      },
      "properties": {
        "text": "button_text_if_any",
        "placeholder": "input_placeholder_if_any",
        "state": "default|hover|active|disabled",
        "variant": "primary|secondary|outline|ghost"
      }
    }
  ]${includeHierarchy ? `,
  "hierarchy": {
    "component_id": {
      "parentId": "parent_component_id_or_null",
      "childIds": ["child1_id", "child2_id"],
      "depth": 0,
      "containerType": "flex|grid|absolute|relative"
    }
  }` : ''}
}

Guidelines:
- Use normalized coordinates (0-1 range) for bounding boxes
- Be precise with component types and confident in classifications
- Include text content when visible
- Detect nested relationships when possible
- Focus on interactive and structural elements`

    const modeSpecificInstructions = {
      sketch: 'This is a hand-drawn sketch or wireframe. Focus on basic shapes and layout structure.',
      cartoon: 'This is a cartoon-style interface. Look for stylized UI elements.',
      anime: 'This is an anime-style interface. Identify UI elements with anime aesthetic.',
      ui: 'This is a standard digital UI. Identify all interactive and layout components.',
      auto: 'Automatically determine the style and analyze accordingly.'
    }

    return `${basePrompt}\n\nSpecific instructions: ${modeSpecificInstructions[mode as keyof typeof modeSpecificInstructions]}`
  }

  async analyzeImage(
    imageBuffer: Buffer,
    options: ProcessingOptions,
    userId: string
  ): Promise<VisionAnalysisResult> {
    // Check rate limiting first
    if (!this.checkRateLimit(userId)) {
      throw new AppError(
        'Rate limit exceeded. Please wait before making another request.',
        'RATE_LIMIT_EXCEEDED',
        ErrorCategory.RATE_LIMIT,
        ErrorSeverity.MEDIUM,
        true,
        RetryStrategy.FIXED,
        FallbackStrategy.CACHED_RESPONSE
      )
    }

    // Create cache key for potential fallback
    const cacheKey = `openai-analysis:${userId}:${Buffer.from(imageBuffer).toString('base64').slice(0, 32)}`

    // Execute with enhanced error handling
    const result = await enhancedErrorHandling.executeWithErrorHandling(
      async () => {
        // Mock mode fallback
        if (!this.client) {
          console.log('🔄 Using mock OpenAI analysis')
          return this.generateMockAnalysis(options, 0)
        }

        return await this.performVisionAnalysis(imageBuffer, options)
      },
      {
        operationName: 'openai-image-analysis',
        userId,
        metadata: {
          mode: options.mode,
          includeHierarchy: options.includeHierarchy,
          imageSize: imageBuffer.length
        },
        ...ErrorHandlingPresets.externalAPI,
        fallbackConfig: {
          strategy: FallbackStrategy.MOCK_DATA,
          enabled: true,
          timeout: 10000,
          cacheKey
        }
      }
    )

    if (result.success) {
      // Cache successful result for future fallback use
      await enhancedErrorHandling.cacheResponse(cacheKey, result.data!, 3600) // 1 hour TTL
      return result.data!
    } else {
      // If all error handling failed, throw the final error
      throw result.error || new AppError(
        'Image analysis failed after all retry and fallback attempts',
        'ANALYSIS_FAILED',
        ErrorCategory.EXTERNAL_SERVICE,
        ErrorSeverity.HIGH
      )
    }
  }

  private detectMimeType(buffer: Buffer): string {
    // Simple MIME type detection based on file headers
    if (buffer.subarray(0, 4).toString('hex') === '89504e47') return 'image/png'
    if (buffer.subarray(0, 3).toString('hex') === 'ffd8ff') return 'image/jpeg'
    if (buffer.subarray(0, 6).toString() === 'GIF87a' || buffer.subarray(0, 6).toString() === 'GIF89a') return 'image/gif'
    if (buffer.subarray(0, 12).toString().includes('WEBP')) return 'image/webp'
    
    return 'image/jpeg' // Default fallback
  }

  private processAnalysisResult(
    rawResult: any,
    options: ProcessingOptions,
    processingTime: number
  ): VisionAnalysisResult {
    // Filter components by confidence threshold
    const filteredComponents = (rawResult.components || [])
      .filter((comp: any) => comp.confidence >= options.minConfidence)
      .slice(0, options.maxComponents)
      .map((comp: any, index: number) => ({
        id: comp.id || `component_${index}`,
        type: comp.type || 'unknown',
        label: comp.label || `Component ${index + 1}`,
        confidence: Math.min(Math.max(comp.confidence || 0.5, 0), 1),
        boundingBox: {
          x: Math.min(Math.max(comp.boundingBox?.x || 0, 0), 1),
          y: Math.min(Math.max(comp.boundingBox?.y || 0, 0), 1),
          width: Math.min(Math.max(comp.boundingBox?.width || 0.1, 0), 1),
          height: Math.min(Math.max(comp.boundingBox?.height || 0.1, 0), 1),
        },
        properties: comp.properties || {}
      }))

    // Process hierarchy if included
    const hierarchy = options.includeHierarchy ? (rawResult.hierarchy || {}) : {}

    return {
      components: filteredComponents,
      hierarchy,
      metadata: {
        imageWidth: 0, // Will be set by caller
        imageHeight: 0, // Will be set by caller
        processingTime,
        modelUsed: 'gpt-4o-mini',
        confidence: filteredComponents.length > 0 
          ? filteredComponents.reduce((sum, comp) => sum + comp.confidence, 0) / filteredComponents.length
          : 0
      }
    }
  }

  private generateMockAnalysis(options: ProcessingOptions, processingTime: number): VisionAnalysisResult {
    const mockComponents: ComponentDetection[] = [
      {
        id: 'header_1',
        type: 'container',
        label: 'Header Container',
        confidence: 0.95,
        boundingBox: { x: 0, y: 0, width: 1, height: 0.1 },
        properties: { containerType: 'flex' }
      },
      {
        id: 'nav_1',
        type: 'navigation',
        label: 'Main Navigation',
        confidence: 0.92,
        boundingBox: { x: 0.1, y: 0.02, width: 0.8, height: 0.06 },
        properties: { orientation: 'horizontal' }
      },
      {
        id: 'button_1',
        type: 'button',
        label: 'Primary Action Button',
        confidence: 0.88,
        boundingBox: { x: 0.4, y: 0.5, width: 0.2, height: 0.08 },
        properties: { variant: 'primary', text: 'Get Started' }
      }
    ]

    const mockHierarchy = options.includeHierarchy ? {
      'header_1': { parentId: null, childIds: ['nav_1'], depth: 0, containerType: 'flex' },
      'nav_1': { parentId: 'header_1', childIds: [], depth: 1 },
      'button_1': { parentId: null, childIds: [], depth: 0 }
    } : {}

    return {
      components: mockComponents,
      hierarchy: mockHierarchy,
      metadata: {
        imageWidth: 1920,
        imageHeight: 1080,
        processingTime,
        modelUsed: 'mock-gpt-4o-mini',
        confidence: 0.92
      }
    }
  }

  /**
   * Perform the actual vision analysis with OpenAI API
   */
  private async performVisionAnalysis(
    imageBuffer: Buffer,
    options: ProcessingOptions
  ): Promise<VisionAnalysisResult> {
    const startTime = Date.now()

    // Convert buffer to base64
    const base64Image = imageBuffer.toString('base64')
    const mimeType = this.detectMimeType(imageBuffer)

    // Generate prompt based on options
    const prompt = this.generatePrompt(options.mode, options.includeHierarchy)

    console.log(`🤖 Analyzing image with OpenAI Vision (mode: ${options.mode})`)

    try {
      // Call OpenAI Vision API with timeout handling
      const response = await this.client!.chat.completions.create({
        model: 'gpt-4o-mini',
        messages: [
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              {
                type: 'image_url',
                image_url: {
                  url: `data:${mimeType};base64,${base64Image}`,
                  detail: 'high'
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        temperature: 0.1, // Low temperature for consistent results
      })

      const content = response.choices[0]?.message?.content
      if (!content) {
        throw new AppError(
          'No response content from OpenAI',
          'OPENAI_NO_CONTENT',
          ErrorCategory.API,
          ErrorSeverity.HIGH,
          true
        )
      }

      // Parse JSON response
      let analysisResult: any
      try {
        // Extract JSON from response (in case there's additional text)
        const jsonMatch = content.match(/\{[\s\S]*\}/)
        if (!jsonMatch) {
          throw new AppError(
            'No valid JSON found in OpenAI response',
            'OPENAI_INVALID_JSON',
            ErrorCategory.API,
            ErrorSeverity.HIGH,
            true
          )
        }
        analysisResult = JSON.parse(jsonMatch[0])
      } catch (parseError) {
        console.error('Failed to parse OpenAI response:', content)
        throw new AppError(
          'Invalid JSON response from OpenAI',
          'OPENAI_PARSE_ERROR',
          ErrorCategory.API,
          ErrorSeverity.HIGH,
          true,
          RetryStrategy.EXPONENTIAL,
          FallbackStrategy.MOCK_DATA
        )
      }

      // Validate and process the result
      const processedResult = this.processAnalysisResult(
        analysisResult,
        options,
        Date.now() - startTime
      )

      console.log(`✅ OpenAI analysis completed in ${Date.now() - startTime}ms`)
      return processedResult

    } catch (error) {
      // Convert various error types to appropriate AppErrors
      if (error instanceof AppError) {
        throw error
      }

      // Handle OpenAI-specific errors
      if (error.status) {
        throw ErrorFactory.createFromHttpStatus(error.status, error.message, {
          service: 'openai',
          operation: 'vision-analysis'
        })
      }

      // Handle network errors
      if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
        throw ErrorFactory.createNetworkError(error.message, {
          service: 'openai',
          operation: 'vision-analysis'
        })
      }

      // Default error
      throw new AppError(
        `OpenAI API error: ${error.message}`,
        'OPENAI_API_ERROR',
        ErrorCategory.EXTERNAL_SERVICE,
        ErrorSeverity.HIGH,
        true,
        RetryStrategy.EXPONENTIAL,
        FallbackStrategy.MOCK_DATA
      )
    }
  }

  /**
   * Setup mock data provider for fallback scenarios
   */
  private setupMockDataProvider(): void {
    enhancedErrorHandling.registerMockDataProvider('openai-image-analysis', (context) => {
      console.log('🎭 Generating mock analysis data for fallback')
      return this.generateMockAnalysis(
        { mode: 'auto', includeHierarchy: true, minConfidence: 0.5, maxComponents: 50 },
        0
      )
    })

    enhancedErrorHandling.registerMockDataProvider('openai-connection-test', () => {
      console.log('🎭 Mock connection test successful')
      return { success: true, mock: true }
    })
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        return false
      }

      if (!this.client) {
        return true // Mock mode is healthy
      }

      // Health check with enhanced error handling
      const result = await enhancedErrorHandling.executeWithErrorHandling(
        async () => {
          await this.client!.chat.completions.create({
            model: 'gpt-4o-mini',
            messages: [{ role: 'user', content: 'ping' }],
            max_tokens: 1,
          })
          return true
        },
        {
          operationName: 'openai-health-check',
          retryConfig: {
            strategy: RetryStrategy.EXPONENTIAL,
            maxAttempts: 2,
            baseDelay: 1000,
            maxDelay: 5000
          },
          circuitBreakerConfig: {
            enabled: false // Don't use circuit breaker for health checks
          },
          fallbackConfig: {
            strategy: FallbackStrategy.NONE,
            enabled: false
          }
        }
      )

      return result.success
    } catch (error) {
      console.error('OpenAI health check failed:', error)
      return false
    }
  }
}

// Export singleton instance
export const OpenAIService = new OpenAIServiceClass()
export type { VisionAnalysisResult, ComponentDetection, ProcessingOptions }
