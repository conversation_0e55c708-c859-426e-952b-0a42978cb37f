/**
 * Basic Error Handling Tests
 * 
 * Simple tests to verify the error handling system is working correctly
 */

import { describe, test, expect, beforeEach, vi } from 'vitest'
import { 
  AppError,
  ErrorCategory,
  ErrorSeverity,
  RetryStrategy,
  FallbackStrategy,
  ErrorFactory
} from '../types'

import { RetryManager } from '../retry-manager'
import { CircuitBreaker } from '../circuit-breaker'
import { enhancedErrorHandling } from '../index'

// Mock Redis
vi.mock('redis', () => ({
  createClient: vi.fn(() => ({
    connect: vi.fn().mockResolvedValue(undefined),
    get: vi.fn().mockResolvedValue(null),
    set: vi.fn().mockResolvedValue('OK'),
    setEx: vi.fn().mockResolvedValue('OK'),
    del: vi.fn().mockResolvedValue(1),
    exists: vi.fn().mockResolvedValue(0),
    ping: vi.fn().mockResolvedValue('PONG'),
    on: vi.fn(),
    off: vi.fn(),
    quit: vi.fn().mockResolvedValue('OK'),
    isReady: true,
    isOpen: true
  }))
}))

describe('Basic Error Handling', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('should create AppError correctly', () => {
    const error = new AppError(
      'Test error',
      'TEST_ERROR',
      ErrorCategory.API,
      ErrorSeverity.HIGH
    )

    expect(error.message).toBe('Test error')
    expect(error.code).toBe('TEST_ERROR')
    expect(error.category).toBe(ErrorCategory.API)
    expect(error.severity).toBe(ErrorSeverity.HIGH)
  })

  test('should create errors from HTTP status', () => {
    const error404 = ErrorFactory.createFromHttpStatus(404, 'Not found')
    expect(error404.category).toBe(ErrorCategory.API)

    const error500 = ErrorFactory.createFromHttpStatus(500, 'Server error')
    expect(error500.category).toBe(ErrorCategory.API) // 500 errors are categorized as API errors
    expect(error500.severity).toBe(ErrorSeverity.HIGH)
  })

  test('should execute simple retry logic', async () => {
    // Use real timers for this test
    vi.useRealTimers()

    const retryManager = new RetryManager()
    let attempts = 0

    const operation = async () => {
      attempts++
      if (attempts < 2) {
        throw new AppError('Temp error', 'TEMP', ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, true)
      }
      return 'success'
    }

    const result = await retryManager.execute(operation, 'test-op', {
      strategy: RetryStrategy.FIXED,
      maxAttempts: 3,
      baseDelay: 10
    })

    expect(result.success).toBe(true)
    expect(result.result).toBe('success')
    expect(attempts).toBe(2)

    // Restore fake timers
    vi.useFakeTimers()
  }, 15000)

  test('should handle circuit breaker basic functionality', async () => {
    const circuitBreaker = new CircuitBreaker('test-breaker', {
      failureThreshold: 2,
      recoveryTimeout: 100,
      enabled: true
    })

    expect(circuitBreaker.getState()).toBe('closed')

    // Trigger failures
    for (let i = 0; i < 2; i++) {
      try {
        await circuitBreaker.execute(async () => {
          throw new Error('Service down')
        })
      } catch (error) {
        // Expected to fail
      }
    }

    expect(circuitBreaker.getState()).toBe('open')
  })

  test('should execute operation with enhanced error handling', async () => {
    // Use real timers for this test
    vi.useRealTimers()

    const operation = async () => 'test-result'

    const result = await enhancedErrorHandling.executeWithErrorHandling(operation, {
      operationName: 'simple-test',
      retryConfig: {
        strategy: RetryStrategy.FIXED,
        maxAttempts: 1,
        baseDelay: 10
      },
      fallbackConfig: {
        strategy: FallbackStrategy.NONE,
        enabled: false
      }
    })

    expect(result.success).toBe(true)
    expect(result.data).toBe('test-result')
    expect(result.operationName).toBe('simple-test')

    // Restore fake timers
    vi.useFakeTimers()
  }, 15000)

  test('should perform health check', async () => {
    const health = await enhancedErrorHandling.healthCheck()
    
    expect(health).toHaveProperty('status')
    expect(health).toHaveProperty('components')
    expect(['healthy', 'degraded', 'unhealthy']).toContain(health.status)
  })
})
