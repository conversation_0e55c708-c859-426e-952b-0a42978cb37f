import { Server as SocketIOServer } from 'socket.io';
import { Server as HttpServer } from 'http';
import { QueueService } from './queue';

interface JobProgressData {
  jobId: string;
  progress: number;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
  message?: string;
  error?: string;
  results?: any;
}

class WebSocketServiceClass {
  private io: SocketIOServer | null = null;
  private isInitialized: boolean = false;

  async initialize(httpServer: HttpServer): Promise<void> {
    try {
      console.log('🔧 Initializing WebSocket service...');

      // Create Socket.IO server
      this.io = new SocketIOServer(httpServer, {
        cors: {
          origin: ['http://localhost:3000', 'http://127.0.0.1:3000'],
          methods: ['GET', 'POST'],
          credentials: true,
        },
        path: '/ws/socket.io/',
        allowEIO3: true, // Allow Engine.IO v3 clients
      });

      // Set up connection handling
      this.setupConnectionHandling();

      // Set up job progress listeners
      this.setupJobProgressListeners();

      this.isInitialized = true;
      console.log('✓ WebSocket service initialized successfully');

    } catch (error) {
      console.error('Failed to initialize WebSocket service:', error);
      throw error;
    }
  }

  private setupConnectionHandling(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      console.log(`🔌 Client connected: ${socket.id}`);

      // Handle client joining job rooms for updates
      socket.on('join-job', (jobId: string) => {
        socket.join(`job-${jobId}`);
        console.log(`📋 Client ${socket.id} joined job room: ${jobId}`);
      });

      // Handle client leaving job rooms
      socket.on('leave-job', (jobId: string) => {
        socket.leave(`job-${jobId}`);
        console.log(`📋 Client ${socket.id} left job room: ${jobId}`);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        console.log(`🔌 Client disconnected: ${socket.id}`);
      });
    });
  }

  private setupJobProgressListeners(): void {
    // Listen for Bull queue progress events
    // This will be called when jobs emit progress updates
    const imageQueue = QueueService.getImageProcessingQueue();
    if (imageQueue) {
      console.log('🔗 Setting up WebSocket listeners for image processing queue');

      imageQueue.on('progress', (job, progress) => {
        console.log(`📊 Queue progress event: Job ${job.id} - ${progress}%`);
        this.broadcastJobProgress({
          jobId: job.id,
          progress: progress,
          status: 'RUNNING',
          message: `Processing... ${Math.round(progress)}%`,
        });
      });

      imageQueue.on('completed', (job, result) => {
        console.log(`✅ Queue completed event: Job ${job.id}`);
        this.broadcastJobProgress({
          jobId: job.id,
          progress: 100,
          status: 'COMPLETED',
          message: 'Processing completed successfully',
          results: result,
        });
      });

      imageQueue.on('failed', (job, error) => {
        console.log(`❌ Queue failed event: Job ${job.id} - ${error.message}`);
        this.broadcastJobProgress({
          jobId: job.id,
          progress: 0,
          status: 'FAILED',
          message: 'Processing failed',
          error: error.message,
        });
      });
    } else {
      console.warn('⚠️ Image processing queue not available for WebSocket listeners');
    }

    // Set up listeners for multi-stage processing queues
    this.setupMultiStageQueueListeners();
  }

  private setupMultiStageQueueListeners(): void {
    const queues = [
      { queue: QueueService.getImagePreprocessingQueue(), name: 'Image Preprocessing', stage: 1 },
      { queue: QueueService.getComponentDetectionQueue(), name: 'Component Detection', stage: 2 },
      { queue: QueueService.getLayoutAnalysisQueue(), name: 'Layout Analysis', stage: 3 },
      { queue: QueueService.getDesignTokenExtractionQueue(), name: 'Design Token Extraction', stage: 4 },
      { queue: QueueService.getQualityAssessmentQueue(), name: 'Quality Assessment', stage: 5 },
      { queue: QueueService.getResultAggregationQueue(), name: 'Result Aggregation', stage: 6 }
    ];

    queues.forEach(({ queue, name, stage }) => {
      if (queue) {
        console.log(`🔗 Setting up WebSocket listeners for ${name} queue`);

        queue.on('progress', (job, progress) => {
          // Calculate overall progress across all stages (each stage is ~16.67% of total)
          const stageWeight = 100 / 6; // 6 stages total
          const overallProgress = Math.round(((stage - 1) * stageWeight) + (progress * stageWeight / 100));

          console.log(`📊 [Stage ${stage}] ${name} progress: ${progress}% (Overall: ${overallProgress}%)`);
          this.broadcastJobProgress({
            jobId: job.id,
            progress: overallProgress,
            status: 'RUNNING',
            message: `[Stage ${stage}/6] ${name}... ${Math.round(progress)}%`,
          });
        });

        queue.on('completed', (job, result) => {
          const stageWeight = 100 / 6;
          const overallProgress = Math.round(stage * stageWeight);

          console.log(`✅ [Stage ${stage}] ${name} completed`);
          this.broadcastJobProgress({
            jobId: job.id,
            progress: overallProgress,
            status: 'RUNNING',
            message: `[Stage ${stage}/6] ${name} completed`,
            results: result,
          });
        });

        queue.on('failed', (job, error) => {
          console.log(`❌ [Stage ${stage}] ${name} failed: ${error.message}`);
          this.broadcastJobProgress({
            jobId: job.id,
            progress: 0,
            status: 'FAILED',
            message: `[Stage ${stage}/6] ${name} failed`,
            error: error.message,
          });
        });
      } else {
        console.warn(`⚠️ ${name} queue not available for WebSocket listeners`);
      }
    });
  }

  // Method to set up listeners after queue is initialized
  setupQueueListeners(): void {
    this.setupJobProgressListeners();
  }

  // Broadcast job progress to all clients in the job room
  broadcastJobProgress(data: JobProgressData): void {
    if (!this.io || !this.isInitialized) {
      console.warn('WebSocket service not initialized, cannot broadcast progress');
      return;
    }

    const room = `job-${data.jobId}`;
    this.io.to(room).emit('job-progress', data);
    
    console.log(`📡 Broadcasting progress for job ${data.jobId}: ${data.progress}% - ${data.status}`);
  }

  // Send message to specific client
  sendToClient(socketId: string, event: string, data: any): void {
    if (!this.io || !this.isInitialized) {
      console.warn('WebSocket service not initialized, cannot send message');
      return;
    }

    this.io.to(socketId).emit(event, data);
  }

  // Broadcast to all connected clients
  broadcast(event: string, data: any): void {
    if (!this.io || !this.isInitialized) {
      console.warn('WebSocket service not initialized, cannot broadcast');
      return;
    }

    this.io.emit(event, data);
  }

  // Get connection status
  isConnected(): boolean {
    return this.isInitialized && this.io !== null;
  }

  // Get number of connected clients
  getConnectedClientsCount(): number {
    if (!this.io || !this.isInitialized) return 0;
    return this.io.engine.clientsCount;
  }

  // Disconnect all clients and close server
  async disconnect(): Promise<void> {
    if (this.io) {
      console.log('🔌 Disconnecting WebSocket service...');
      this.io.close();
      this.io = null;
      this.isInitialized = false;
      console.log('✓ WebSocket service disconnected');
    }
  }
}

// Export singleton instance
export const WebSocketService = new WebSocketServiceClass();
