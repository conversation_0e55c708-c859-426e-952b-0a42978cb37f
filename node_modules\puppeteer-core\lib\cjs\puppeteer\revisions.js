"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '138.0.7204.168',
    'chrome-headless-shell': '138.0.7204.168',
    firefox: 'stable_141.0',
});
//# sourceMappingURL=revisions.js.map