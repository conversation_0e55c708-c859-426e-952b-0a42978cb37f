import { DatabaseService } from './database';
import { RedisService } from './redis';

interface AnalysisResultData {
  imageId: string;
  userId: string;
  analysisType: string;
  processingMode: string;
  results: any;
  confidence?: number;
  processingTime?: number;
  componentCount?: number;
  qualityScore?: number;
}

interface ComponentDetectionData {
  imageId: string;
  userId: string;
  components: any[];
  hierarchy: any;
  boundingBoxes: any[];
  detectionModel?: string;
  confidence: number;
  processingTime: number;
}

interface DesignTokenData {
  imageId: string;
  userId: string;
  colors: any;
  typography: any;
  spacing: any;
  components: any;
  breakpoints: any;
  extractionMethod?: string;
  confidence: number;
  tokenCount: number;
}

class AnalysisStorageServiceClass {
  // ============================================================================
  // ANALYSIS RESULTS MANAGEMENT
  // ============================================================================

  async storeAnalysisResult(data: AnalysisResultData): Promise<string | null> {
    try {
      const result = await DatabaseService.createAnalysisResult({
        imageId: data.imageId,
        userId: data.userId,
        analysisType: data.analysisType,
        processingMode: data.processingMode,
        results: JSON.stringify(data.results),
        confidence: data.confidence,
        processingTime: data.processingTime,
        componentCount: data.componentCount,
        qualityScore: data.qualityScore,
        version: '1.0'
      });

      console.log(`📊 Stored ${data.analysisType} analysis result for image ${data.imageId}`);
      return result.id;
    } catch (error) {
      console.error('Failed to store analysis result:', error);
      return null;
    }
  }

  async getAnalysisResult(imageId: string, analysisType: string): Promise<any | null> {
    try {
      // Try cache first
      const cacheKey = `analysis:${imageId}:${analysisType}`;
      const cached = await RedisService.get(cacheKey);
      if (cached) {
        console.log(`📦 Retrieved ${analysisType} analysis from cache for image ${imageId}`);
        return cached;
      }

      // Fallback to database
      const result = await DatabaseService.getAnalysisResult(imageId, analysisType);
      if (result) {
        const parsedResults = JSON.parse(result.results);
        
        // Cache for future requests
        await RedisService.set(cacheKey, parsedResults, { ttl: 3600 });
        
        console.log(`📊 Retrieved ${analysisType} analysis from database for image ${imageId}`);
        return parsedResults;
      }

      return null;
    } catch (error) {
      console.error(`Failed to get ${analysisType} analysis for image ${imageId}:`, error);
      return null;
    }
  }

  // ============================================================================
  // COMPONENT DETECTION STORAGE
  // ============================================================================

  async storeComponentDetection(data: ComponentDetectionData): Promise<string | null> {
    try {
      const result = await DatabaseService.createComponentDetection({
        imageId: data.imageId,
        userId: data.userId,
        components: JSON.stringify(data.components),
        hierarchy: JSON.stringify(data.hierarchy),
        boundingBoxes: JSON.stringify(data.boundingBoxes),
        detectionModel: data.detectionModel || 'gpt-4o-mini',
        confidence: data.confidence,
        processingTime: data.processingTime
      });

      // Also cache in Redis for fast access
      await RedisService.cacheProcessedComponents(
        data.imageId, 
        data.components, 
        data.hierarchy, 
        7200
      );

      console.log(`🔍 Stored component detection for image ${data.imageId} (${data.components.length} components)`);
      return result.id;
    } catch (error) {
      console.error('Failed to store component detection:', error);
      return null;
    }
  }

  async getComponentDetection(imageId: string): Promise<{ components: any[], hierarchy: any } | null> {
    try {
      // Try cache first
      const cached = await RedisService.getProcessedComponents(imageId);
      if (cached) {
        return cached;
      }

      // Fallback to database
      const result = await DatabaseService.getComponentDetection(imageId);
      if (result) {
        const components = JSON.parse(result.components);
        const hierarchy = JSON.parse(result.hierarchy);
        
        // Cache for future requests
        await RedisService.cacheProcessedComponents(imageId, components, hierarchy, 7200);
        
        console.log(`🔍 Retrieved component detection from database for image ${imageId}`);
        return { components, hierarchy };
      }

      return null;
    } catch (error) {
      console.error(`Failed to get component detection for image ${imageId}:`, error);
      return null;
    }
  }

  // ============================================================================
  // DESIGN TOKENS STORAGE
  // ============================================================================

  async storeDesignTokens(data: DesignTokenData): Promise<string | null> {
    try {
      const result = await DatabaseService.createDesignTokens({
        imageId: data.imageId,
        userId: data.userId,
        colors: JSON.stringify(data.colors),
        typography: JSON.stringify(data.typography),
        spacing: JSON.stringify(data.spacing),
        components: JSON.stringify(data.components),
        breakpoints: JSON.stringify(data.breakpoints),
        extractionMethod: data.extractionMethod || 'ai-analysis',
        confidence: data.confidence,
        tokenCount: data.tokenCount
      });

      // Also cache in Redis for fast access
      await RedisService.cacheDesignTokens(data.imageId, {
        colors: data.colors,
        typography: data.typography,
        spacing: data.spacing,
        components: data.components,
        breakpoints: data.breakpoints
      }, 7200);

      console.log(`🎨 Stored design tokens for image ${data.imageId} (${data.tokenCount} tokens)`);
      return result.id;
    } catch (error) {
      console.error('Failed to store design tokens:', error);
      return null;
    }
  }

  async getDesignTokens(imageId: string): Promise<any | null> {
    try {
      // Try cache first
      const cached = await RedisService.getDesignTokens(imageId);
      if (cached) {
        return cached;
      }

      // Fallback to database
      const result = await DatabaseService.getDesignTokens(imageId);
      if (result) {
        const tokens = {
          colors: JSON.parse(result.colors),
          typography: JSON.parse(result.typography),
          spacing: JSON.parse(result.spacing),
          components: JSON.parse(result.components),
          breakpoints: JSON.parse(result.breakpoints)
        };
        
        // Cache for future requests
        await RedisService.cacheDesignTokens(imageId, tokens, 7200);
        
        console.log(`🎨 Retrieved design tokens from database for image ${imageId}`);
        return tokens;
      }

      return null;
    } catch (error) {
      console.error(`Failed to get design tokens for image ${imageId}:`, error);
      return null;
    }
  }

  // ============================================================================
  // BATCH OPERATIONS AND CLEANUP
  // ============================================================================

  async batchStoreResults(imageId: string, userId: string, allResults: any): Promise<boolean> {
    try {
      const operations = [];

      // Store component detection if available
      if (allResults.componentDetection) {
        operations.push(this.storeComponentDetection({
          imageId,
          userId,
          ...allResults.componentDetection
        }));
      }

      // Store design tokens if available
      if (allResults.designTokens) {
        operations.push(this.storeDesignTokens({
          imageId,
          userId,
          ...allResults.designTokens
        }));
      }

      // Store other analysis results
      if (allResults.layoutAnalysis) {
        operations.push(this.storeAnalysisResult({
          imageId,
          userId,
          analysisType: 'LAYOUT_ANALYSIS',
          processingMode: 'MULTI_STAGE',
          results: allResults.layoutAnalysis
        }));
      }

      if (allResults.qualityAssessment) {
        operations.push(this.storeAnalysisResult({
          imageId,
          userId,
          analysisType: 'QUALITY_ASSESSMENT',
          processingMode: 'MULTI_STAGE',
          results: allResults.qualityAssessment
        }));
      }

      await Promise.all(operations);
      console.log(`📊 Batch stored all analysis results for image ${imageId}`);
      return true;
    } catch (error) {
      console.error(`Failed to batch store results for image ${imageId}:`, error);
      return false;
    }
  }

  async cleanupImageAnalysis(imageId: string): Promise<boolean> {
    try {
      // Clean up database records
      await DatabaseService.deleteAnalysisResults(imageId);
      
      // Clean up Redis cache
      await RedisService.cleanupImageCache(imageId);
      
      console.log(`🧹 Cleaned up all analysis data for image ${imageId}`);
      return true;
    } catch (error) {
      console.error(`Failed to cleanup analysis data for image ${imageId}:`, error);
      return false;
    }
  }

  async getAnalysisStats(): Promise<any> {
    try {
      const dbStats = await DatabaseService.getAnalysisStats();
      const cacheStats = await RedisService.getCacheStats();
      
      return {
        database: dbStats,
        cache: cacheStats,
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('Failed to get analysis stats:', error);
      return null;
    }
  }
}

export const AnalysisStorageService = new AnalysisStorageServiceClass();
