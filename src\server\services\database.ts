import { PrismaClient } from '@prisma/client'

class DatabaseServiceClass {
  private prisma: PrismaClient | null = null

  async initialize(): Promise<void> {
    try {
      this.prisma = new PrismaClient({
        log: process.env.NODE_ENV === 'development'
          ? ['query', 'info', 'warn', 'error']
          : ['warn', 'error'],
      })

      // Test the connection
      await this.prisma.$connect()

      console.log('✓ Database connection established successfully')
    } catch (error) {
      console.error('Failed to connect to database:', error)
      throw error
    }
  }

  async disconnect(): Promise<void> {
    if (this.prisma) {
      await this.prisma.$disconnect()
      this.prisma = null
      console.log('Database connection closed')
    }
  }

  get client(): PrismaClient {
    if (!this.prisma) {
      throw new Error('Database not initialized. Call initialize() first.')
    }
    return this.prisma
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.prisma) {
        return false
      }

      await this.prisma.$queryRaw`SELECT 1`
      return true
    } catch (error) {
      console.error('Database health check failed:', error)
      return false
    }
  }

  // Simple helper methods for Phase 1
  async createUser(data: { email: string; name?: string }) {
    return this.client.user.create({ data })
  }

  async createImage(data: {
    userId: string
    projectId?: string | null
    filename: string
    originalFilename: string
    mimeType: string
    size: number
    width: number
    height: number
    s3Key: string
    s3Bucket: string
    mode?: string
    previewUrl?: string
    thumbnailUrl?: string
  }) {
    return this.client.image.create({
      data: {
        ...data,
        processingStatus: 'PENDING', // Default status
        metadata: JSON.stringify({}), // Empty metadata initially
      }
    })
  }

  async findImageById(id: string) {
    return this.client.image.findUnique({ where: { id } })
  }

  async findImagesByUserId(userId: string) {
    return this.client.image.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    })
  }

  async getUserImages(userId: string, options: {
    page: number;
    limit: number;
    projectId?: string;
    status?: string
  }) {
    const { page, limit, projectId, status } = options
    const skip = (page - 1) * limit

    const where: any = { userId }
    if (projectId) {
      where.projectId = projectId
    }
    if (status) {
      where.status = status
    }

    const [images, totalCount] = await Promise.all([
      this.client.image.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          project: {
            select: {
              id: true,
              name: true
            }
          }
        }
      }),
      this.client.image.count({ where })
    ])

    return {
      images,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  }

  // User management methods
  async getUserById(userId: string) {
    return this.client.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        subscription: true,
        tokenBalance: true,
        isActive: true,
        preferences: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
      }
    })
  }

  async getUserStats(userId: string) {
    const [user, imageCount, projectCount, tokenTransactions] = await Promise.all([
      this.client.user.findUnique({
        where: { id: userId },
        select: { tokenBalance: true, subscription: true, createdAt: true }
      }),
      this.client.image.count({ where: { userId } }),
      this.client.project.count({ where: { userId } }),
      this.client.tokenTransaction.aggregate({
        where: { userId },
        _sum: { amount: true }
      })
    ])

    return {
      tokenBalance: user?.tokenBalance || 0,
      subscription: user?.subscription || 'FREE',
      memberSince: user?.createdAt,
      totalImages: imageCount,
      totalProjects: projectCount,
      totalTokensUsed: Math.abs(tokenTransactions._sum.amount || 0),
    }
  }

  async updateUserPreferences(userId: string, preferences: any) {
    // Get current preferences and merge with new ones
    const currentUser = await this.client.user.findUnique({
      where: { id: userId },
      select: { preferences: true }
    })

    let currentPrefs = {}
    if (currentUser?.preferences) {
      try {
        currentPrefs = JSON.parse(currentUser.preferences)
      } catch (e) {
        console.warn('Failed to parse current preferences:', e)
      }
    }

    const mergedPreferences = { ...currentPrefs, ...preferences }

    return this.client.user.update({
      where: { id: userId },
      data: { preferences: JSON.stringify(mergedPreferences) },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        subscription: true,
        tokenBalance: true,
        preferences: true,
        updatedAt: true,
      }
    })
  }

  async getUserTokenData(userId: string, options: { page: number; limit: number }) {
    const { page, limit } = options
    const skip = (page - 1) * limit

    const [user, transactions, totalCount] = await Promise.all([
      this.client.user.findUnique({
        where: { id: userId },
        select: { tokenBalance: true }
      }),
      this.client.tokenTransaction.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          amount: true,
          type: true,
          description: true,
          createdAt: true,
        }
      }),
      this.client.tokenTransaction.count({ where: { userId } })
    ])

    return {
      currentBalance: user?.tokenBalance || 0,
      transactions,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  }

  async getUserActivity(userId: string, options: { page: number; limit: number; action?: string }) {
    const { page, limit, action } = options
    const skip = (page - 1) * limit

    const where: any = { userId }
    if (action) {
      where.action = action
    }

    const [activities, totalCount] = await Promise.all([
      this.client.auditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          action: true,
          resource: true,
          description: true,
          createdAt: true,
        }
      }),
      this.client.auditLog.count({ where })
    ])

    return {
      activities,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  }

  async deleteUser(userId: string, confirmPassword: string) {
    // First verify the password
    const user = await this.client.user.findUnique({
      where: { id: userId },
      select: { password: true }
    })

    if (!user) {
      return false
    }

    const bcrypt = require('bcryptjs')
    const isPasswordValid = await bcrypt.compare(confirmPassword, user.password)

    if (!isPasswordValid) {
      return false
    }

    // Soft delete by setting isActive to false
    await this.client.user.update({
      where: { id: userId },
      data: {
        isActive: false,
        email: `deleted_${Date.now()}_${user.email}` // Prevent email conflicts
      }
    })

    // Log the deletion
    await this.client.auditLog.create({
      data: {
        userId,
        userEmail: `deleted_user_${userId}`,
        action: 'USER_DELETE',
        resource: 'USER',
        resourceId: userId,
        description: 'User account deleted',
      }
    })

    return true
  }

  // Project management methods
  async createProject(data: {
    userId: string
    name: string
    description?: string
    settings?: any
  }) {
    return this.client.project.create({
      data: {
        userId: data.userId,
        name: data.name,
        description: data.description,
        settings: data.settings ? JSON.stringify(data.settings) : null,
        isArchived: false,
      }
    })
  }

  async getUserProjects(userId: string, options: { page: number; limit: number; includeArchived?: boolean }) {
    const { page, limit, includeArchived = false } = options
    const skip = (page - 1) * limit

    const where: any = { userId }
    if (!includeArchived) {
      where.isArchived = false
    }

    const [projects, totalCount] = await Promise.all([
      this.client.project.findMany({
        where,
        orderBy: { updatedAt: 'desc' },
        skip,
        take: limit,
        include: {
          _count: {
            select: { images: true }
          }
        }
      }),
      this.client.project.count({ where })
    ])

    return {
      projects: projects.map(project => ({
        ...project,
        settings: project.settings ? JSON.parse(project.settings) : null,
        imageCount: project._count.images,
      })),
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  }

  async getProjectById(projectId: string, userId: string) {
    const project = await this.client.project.findFirst({
      where: {
        id: projectId,
        userId: userId
      },
      include: {
        images: {
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            filename: true,
            originalFilename: true,
            size: true,
            width: true,
            height: true,
            processingStatus: true,
            previewUrl: true,
            thumbnailUrl: true,
            createdAt: true,
          }
        },
        _count: {
          select: { images: true }
        }
      }
    })

    if (!project) return null

    return {
      ...project,
      settings: project.settings ? JSON.parse(project.settings) : null,
      imageCount: project._count.images,
    }
  }

  async updateProject(projectId: string, userId: string, data: {
    name?: string
    description?: string
    settings?: any
    isArchived?: boolean
  }) {
    const updateData: any = {}

    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.settings !== undefined) updateData.settings = JSON.stringify(data.settings)
    if (data.isArchived !== undefined) updateData.isArchived = data.isArchived

    return this.client.project.update({
      where: {
        id: projectId,
        userId: userId
      },
      data: updateData
    })
  }

  async deleteProject(projectId: string, userId: string) {
    // Check if project exists and belongs to user
    const project = await this.client.project.findFirst({
      where: {
        id: projectId,
        userId: userId
      }
    })

    if (!project) return false

    // Delete all associated images first
    await this.client.image.deleteMany({
      where: { projectId }
    })

    // Delete the project
    await this.client.project.delete({
      where: { id: projectId }
    })

    // Log the deletion
    await this.client.auditLog.create({
      data: {
        userId,
        userEmail: '', // Will be filled by the route handler
        action: 'PROJECT_DELETE',
        resource: 'PROJECT',
        resourceId: projectId,
        description: `Project "${project.name}" deleted`,
      }
    })

    return true
  }

  // Processing Job methods
  async createProcessingJob(data: {
    userId: string
    imageId: string
    type: string
    priority?: number
    input?: any
  }) {
    return this.client.processingJob.create({
      data: {
        userId: data.userId,
        imageId: data.imageId,
        type: data.type,
        priority: data.priority || 0,
        input: data.input ? JSON.stringify(data.input) : null,
        status: 'PENDING',
        progress: 0,
      }
    })
  }

  async getProcessingJobs(options: {
    userId: string
    status?: string
    limit?: number
    offset?: number
  }) {
    const { userId, status, limit = 50, offset = 0 } = options

    const where: any = { userId }
    if (status) {
      where.status = status
    }

    return this.client.processingJob.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        user: {
          select: { id: true, email: true, name: true }
        },
        image: {
          select: {
            id: true,
            filename: true,
            originalFilename: true,
            width: true,
            height: true,
            mimeType: true
          }
        }
      }
    })
  }

  async getProcessingJobById(id: string) {
    return this.client.processingJob.findUnique({
      where: { id },
      include: {
        user: {
          select: { id: true, email: true, name: true }
        },
        image: {
          select: {
            id: true,
            filename: true,
            originalFilename: true,
            width: true,
            height: true,
            mimeType: true
          }
        }
      }
    })
  }

  async updateProcessingJob(id: string, data: {
    status?: string
    progress?: number
    output?: any
    error?: string
    startedAt?: Date
    completedAt?: Date
    processingTime?: number
    tokenCost?: number
  }) {
    const updateData: any = {}

    if (data.status !== undefined) updateData.status = data.status
    if (data.progress !== undefined) updateData.progress = data.progress
    if (data.output !== undefined) updateData.output = JSON.stringify(data.output)
    if (data.error !== undefined) updateData.error = data.error
    if (data.startedAt !== undefined) updateData.startedAt = data.startedAt
    if (data.completedAt !== undefined) updateData.completedAt = data.completedAt
    if (data.processingTime !== undefined) updateData.processingTime = data.processingTime
    if (data.tokenCost !== undefined) updateData.tokenCost = data.tokenCost

    return this.client.processingJob.update({
      where: { id },
      data: updateData
    })
  }

  async getImageById(id: string) {
    return this.client.image.findUnique({
      where: { id },
      select: {
        id: true,
        filename: true,
        originalFilename: true,
        width: true,
        height: true,
        mimeType: true,
        size: true,
        processingStatus: true,
        previewUrl: true,
        thumbnailUrl: true,
        createdAt: true,
        processedAt: true,
      }
    })
  }

  // Admin methods
  async getAllUsers(options: {
    page: number
    limit: number
    search?: string
    role?: string
    subscription?: string
    isActive?: boolean
  }) {
    const { page, limit, search, role, subscription, isActive } = options
    const skip = (page - 1) * limit

    const where: any = {}

    if (search) {
      where.OR = [
        { email: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (role) where.role = role
    if (subscription) where.subscription = subscription
    if (isActive !== undefined) where.isActive = isActive

    const [users, totalCount] = await Promise.all([
      this.client.user.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          subscription: true,
          tokenBalance: true,
          isActive: true,
          createdAt: true,
          lastLoginAt: true,
          _count: {
            select: {
              images: true,
              projects: true,
            }
          }
        }
      }),
      this.client.user.count({ where })
    ])

    return {
      users: users.map(user => ({
        ...user,
        imageCount: user._count.images,
        projectCount: user._count.projects,
      })),
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  }

  async updateUserAsAdmin(userId: string, data: {
    name?: string
    email?: string
    role?: string
    subscription?: string
    isActive?: boolean
    tokenBalance?: number
  }) {
    return this.client.user.update({
      where: { id: userId },
      data,
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        subscription: true,
        tokenBalance: true,
        isActive: true,
        updatedAt: true,
      }
    })
  }

  async getSystemStats() {
    const [
      totalUsers,
      activeUsers,
      totalImages,
      totalProjects,
      totalTokenTransactions,
      recentActivity
    ] = await Promise.all([
      this.client.user.count(),
      this.client.user.count({ where: { isActive: true } }),
      this.client.image.count(),
      this.client.project.count(),
      this.client.tokenTransaction.aggregate({
        _sum: { amount: true },
        _count: true
      }),
      this.client.auditLog.findMany({
        orderBy: { createdAt: 'desc' },
        take: 10,
        select: {
          action: true,
          resource: true,
          description: true,
          createdAt: true,
          userEmail: true,
        }
      })
    ])

    return {
      users: {
        total: totalUsers,
        active: activeUsers,
        inactive: totalUsers - activeUsers,
      },
      content: {
        images: totalImages,
        projects: totalProjects,
      },
      tokens: {
        totalTransactions: totalTokenTransactions._count,
        totalAmount: totalTokenTransactions._sum.amount || 0,
      },
      recentActivity,
    }
  }

  async getSystemConfig(key?: string) {
    if (key) {
      return this.client.systemConfig.findUnique({
        where: { key }
      })
    }

    return this.client.systemConfig.findMany({
      orderBy: { category: 'asc' }
    })
  }

  async setSystemConfig(data: {
    key: string
    value: any
    description?: string
    category?: string
    isPublic?: boolean
  }) {
    return this.client.systemConfig.upsert({
      where: { key: data.key },
      update: {
        value: JSON.stringify(data.value),
        description: data.description,
        category: data.category,
        isPublic: data.isPublic,
      },
      create: {
        key: data.key,
        value: JSON.stringify(data.value),
        description: data.description,
        category: data.category || 'general',
        isPublic: data.isPublic || false,
      }
    })
  }

  // ============================================================================
  // ANALYSIS RESULTS MANAGEMENT
  // ============================================================================

  async createAnalysisResult(data: any) {
    return await this.client.analysisResult.create({
      data
    });
  }

  async getAnalysisResult(imageId: string, analysisType: string) {
    return await this.client.analysisResult.findFirst({
      where: {
        imageId,
        analysisType
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async createComponentDetection(data: any) {
    return await this.client.componentDetection.create({
      data
    });
  }

  async getComponentDetection(imageId: string) {
    return await this.client.componentDetection.findFirst({
      where: {
        imageId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async createDesignTokens(data: any) {
    return await this.client.designTokens.create({
      data
    });
  }

  async getDesignTokens(imageId: string) {
    return await this.client.designTokens.findFirst({
      where: {
        imageId
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
  }

  async deleteAnalysisResults(imageId: string) {
    await Promise.all([
      this.client.analysisResult.deleteMany({
        where: { imageId }
      }),
      this.client.componentDetection.deleteMany({
        where: { imageId }
      }),
      this.client.designTokens.deleteMany({
        where: { imageId }
      })
    ]);
  }

  async getAnalysisStats() {
    const [analysisCount, componentCount, tokenCount] = await Promise.all([
      this.client.analysisResult.count(),
      this.client.componentDetection.count(),
      this.client.designTokens.count()
    ]);

    return {
      analysisResults: analysisCount,
      componentDetections: componentCount,
      designTokens: tokenCount,
      total: analysisCount + componentCount + tokenCount
    };
  }
}

export const DatabaseService = new DatabaseServiceClass()
