const puppeteer = require('puppeteer');

// Helper function to click button by text
async function clickButtonByText(page, text) {
  const buttons = await page.$$('button');
  for (const button of buttons) {
    const buttonText = await page.evaluate(el => el.textContent, button);
    if (buttonText.includes(text)) {
      await button.click();
      return true;
    }
  }
  return false;
}

// Helper function to wait for element
async function waitForElement(page, selector, timeout = 5000) {
  try {
    await page.waitForSelector(selector, { timeout });
    return true;
  } catch (error) {
    return false;
  }
}

describe('Infinite Canvas UI Tests', () => {
  let browser;
  let page;
  const baseUrl = global.testConfig.baseUrl;

  beforeAll(async () => {
    browser = await puppeteer.launch({
      headless: global.testConfig.headless,
      slowMo: global.testConfig.slowMo,
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-web-security']
    });
    page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });

    // Set longer timeout for page operations
    page.setDefaultTimeout(global.testConfig.timeout);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  beforeEach(async () => {
    // Clear any previous state
    await page.goto(`${baseUrl}/canvas-test`, { waitUntil: 'networkidle0' });
  });

  describe('Canvas Test Page Loading', () => {
    test('should load canvas test page successfully', async () => {
      await page.goto(`${baseUrl}/canvas-test`, { waitUntil: 'networkidle0' });
      
      // Check if the page title is correct
      const title = await page.title();
      expect(title).toContain('I2D-Convert');
      
      // Check if main elements are present
      const header = await page.$('h1');
      const headerText = await page.evaluate(el => el.textContent, header);
      expect(headerText).toBe('Canvas Test Environment');
      
      // Check if control buttons are present
      const buttons = await page.$$('button');
      expect(buttons.length).toBeGreaterThan(0);

      // Check for specific button text
      const buttonTexts = await Promise.all(
        buttons.map(btn => page.evaluate(el => el.textContent, btn))
      );

      expect(buttonTexts.some(text => text.includes('Load Mock Data'))).toBe(true);
      expect(buttonTexts.some(text => text.includes('Stress Test'))).toBe(true);
      expect(buttonTexts.some(text => text.includes('Clear Canvas'))).toBe(true);
    });

    test('should show instructions overlay when canvas is empty', async () => {
      // Check if instructions overlay is visible
      const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center');
      expect(overlay).toBeTruthy();
      
      // Check if instructions text is present
      const instructionsText = await page.$eval('h3', el => el.textContent);
      expect(instructionsText).toBe('Canvas Test Environment');
    });
  });

  describe('Mock Data Loading', () => {
    test('should load mock data and display components', async () => {
      // Click Load Mock Data button
      const clicked = await clickButtonByText(page, 'Load Mock Data');
      expect(clicked).toBe(true);

      // Wait for canvas to update
      await page.waitForTimeout(1000);
      
      // Check if instructions overlay is hidden
      const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center.bg-black');
      expect(overlay).toBeFalsy();
      
      // Check if components counter is updated
      const statusBar = await page.$('.bg-gray-50.border-t');
      const statusText = await page.evaluate(el => el.textContent, statusBar);
      expect(statusText).toContain('Components: 10');
      
      // Check if Konva stage is present
      const canvas = await page.$('canvas');
      expect(canvas).toBeTruthy();
    });

    test('should display component bounding boxes on canvas', async () => {
      await page.click('button:has-text("Load Mock Data")');
      await page.waitForTimeout(1000);
      
      // Check if canvas elements are rendered
      const canvasElements = await page.$$('canvas');
      expect(canvasElements.length).toBeGreaterThan(0);
      
      // Verify canvas has content (not empty)
      const canvasContent = await page.evaluate(() => {
        const canvas = document.querySelector('canvas');
        if (!canvas) return false;
        
        const ctx = canvas.getContext('2d');
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        
        // Check if canvas has any non-transparent pixels
        for (let i = 3; i < imageData.data.length; i += 4) {
          if (imageData.data[i] !== 0) return true;
        }
        return false;
      });
      
      expect(canvasContent).toBe(true);
    });
  });

  describe('Canvas Interaction', () => {
    beforeEach(async () => {
      await page.click('button:has-text("Load Mock Data")');
      await page.waitForTimeout(1000);
    });

    test('should handle zoom functionality', async () => {
      const canvas = await page.$('canvas');
      expect(canvas).toBeTruthy();
      
      // Get initial zoom level
      const initialZoom = await page.$eval('.text-sm.text-gray-600', el => el.textContent);
      expect(initialZoom).toContain('Zoom: 100%');
      
      // Simulate mouse wheel zoom (zoom in)
      await page.hover('canvas');
      await page.mouse.wheel({ deltaY: -100 });
      await page.waitForTimeout(500);
      
      // Check if zoom level changed
      const newZoom = await page.$eval('.text-sm.text-gray-600', el => el.textContent);
      expect(newZoom).not.toBe(initialZoom);
      expect(newZoom).toContain('Zoom:');
    });

    test('should handle reset view functionality', async () => {
      // Zoom in first
      await page.hover('canvas');
      await page.mouse.wheel({ deltaY: -200 });
      await page.waitForTimeout(500);
      
      // Click reset view button
      await page.click('button:has-text("Reset View")');
      await page.waitForTimeout(500);
      
      // Check if zoom is back to 100%
      const resetZoom = await page.$eval('.text-sm.text-gray-600', el => el.textContent);
      expect(resetZoom).toContain('Zoom: 100%');
    });

    test('should handle properties panel toggle', async () => {
      // Check if properties panel is initially visible
      const initialPanel = await page.$('.w-80.bg-white.border-l');
      expect(initialPanel).toBeTruthy();
      
      // Click properties panel toggle
      await page.click('button:has-text("Properties Panel")');
      await page.waitForTimeout(500);
      
      // Check if properties panel is hidden
      const hiddenPanel = await page.$('.w-80.bg-white.border-l');
      expect(hiddenPanel).toBeFalsy();
      
      // Toggle back on
      await page.click('button:has-text("Properties Panel")');
      await page.waitForTimeout(500);
      
      // Check if properties panel is visible again
      const visiblePanel = await page.$('.w-80.bg-white.border-l');
      expect(visiblePanel).toBeTruthy();
    });
  });

  describe('Component Selection', () => {
    beforeEach(async () => {
      await page.click('button:has-text("Load Mock Data")');
      await page.waitForTimeout(1000);
    });

    test('should show "No Component Selected" message initially', async () => {
      const propertiesPanel = await page.$('.w-80.bg-white.border-l');
      const panelText = await page.evaluate(el => el.textContent, propertiesPanel);
      expect(panelText).toContain('No Component Selected');
    });

    test('should handle component selection via canvas click', async () => {
      // Click on canvas area where components should be
      const canvas = await page.$('canvas');
      const canvasBox = await canvas.boundingBox();
      
      // Click in the center of the canvas
      await page.mouse.click(
        canvasBox.x + canvasBox.width / 2,
        canvasBox.y + canvasBox.height / 2
      );
      
      await page.waitForTimeout(500);
      
      // Check if status bar shows selection (this might not work if no component is at center)
      // This is a basic test - in real scenario we'd need to know component positions
      const statusBar = await page.$('.bg-gray-50.border-t');
      const statusText = await page.evaluate(el => el.textContent, statusBar);
      
      // At minimum, verify the status bar exists and has component count
      expect(statusText).toContain('Components: 10');
    });
  });

  describe('Stress Test', () => {
    test('should load stress test with 50 components', async () => {
      await page.click('button:has-text("Stress Test")');
      await page.waitForTimeout(2000); // Give more time for 50 components
      
      // Check if component count is updated
      const statusBar = await page.$('.bg-gray-50.border-t');
      const statusText = await page.evaluate(el => el.textContent, statusBar);
      expect(statusText).toContain('Components: 50');
      
      // Verify canvas still renders properly with many components
      const canvas = await page.$('canvas');
      expect(canvas).toBeTruthy();
      
      // Test that zoom still works with many components
      await page.hover('canvas');
      await page.mouse.wheel({ deltaY: -100 });
      await page.waitForTimeout(500);
      
      const zoomText = await page.$eval('.text-sm.text-gray-600', el => el.textContent);
      expect(zoomText).toContain('Zoom:');
      expect(zoomText).not.toContain('100%');
    });
  });

  describe('Clear Canvas', () => {
    test('should clear canvas and show instructions overlay', async () => {
      // Load mock data first
      await page.click('button:has-text("Load Mock Data")');
      await page.waitForTimeout(1000);
      
      // Verify components are loaded
      let statusText = await page.$eval('.bg-gray-50.border-t', el => el.textContent);
      expect(statusText).toContain('Components: 10');
      
      // Clear canvas
      await page.click('button:has-text("Clear Canvas")');
      await page.waitForTimeout(1000);
      
      // Check if component count is 0
      statusText = await page.$eval('.bg-gray-50.border-t', el => el.textContent);
      expect(statusText).toContain('Components: 0');
      
      // Check if instructions overlay is back
      const overlay = await page.$('.absolute.inset-0.flex.items-center.justify-center.bg-black');
      expect(overlay).toBeTruthy();
    });
  });

  describe('Responsive Design', () => {
    test('should handle different viewport sizes', async () => {
      // Test mobile viewport
      await page.setViewport({ width: 375, height: 667 });
      await page.reload({ waitUntil: 'networkidle0' });
      
      // Check if page still loads
      const header = await page.$('h1');
      expect(header).toBeTruthy();
      
      // Test tablet viewport
      await page.setViewport({ width: 768, height: 1024 });
      await page.reload({ waitUntil: 'networkidle0' });
      
      // Check if page still loads
      const headerTablet = await page.$('h1');
      expect(headerTablet).toBeTruthy();
      
      // Reset to desktop
      await page.setViewport({ width: 1920, height: 1080 });
    });
  });

  describe('Error Handling', () => {
    test('should handle navigation to non-existent canvas page gracefully', async () => {
      // Try to navigate to a canvas page with invalid ID
      await page.goto(`${baseUrl}/canvas/invalid-id`, { waitUntil: 'networkidle0' });
      
      // Should either show error message or redirect
      // The exact behavior depends on implementation
      const pageContent = await page.content();
      expect(pageContent).toBeTruthy(); // At minimum, page should load
    });
  });
});
