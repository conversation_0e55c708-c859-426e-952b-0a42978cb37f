/**
 * Enhanced Error Handling and Retry Logic System
 * 
 * This module defines comprehensive error types, classification system,
 * and error handling strategies for robust failure management.
 */

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

// Error categories for classification
export enum ErrorCategory {
  NETWORK = 'network',
  API = 'api',
  VALIDATION = 'validation',
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  RATE_LIMIT = 'rate_limit',
  TIMEOUT = 'timeout',
  RESOURCE = 'resource',
  CONFIGURATION = 'configuration',
  EXTERNAL_SERVICE = 'external_service',
  INTERNAL = 'internal',
  USER_INPUT = 'user_input'
}

// Retry strategies
export enum RetryStrategy {
  NONE = 'none',
  FIXED = 'fixed',
  EXPONENTIAL = 'exponential',
  LINEAR = 'linear',
  CUSTOM = 'custom'
}

// Circuit breaker states
export enum CircuitBreakerState {
  CLOSED = 'closed',
  OPEN = 'open',
  HALF_OPEN = 'half_open'
}

// Fallback strategies
export enum FallbackStrategy {
  NONE = 'none',
  MOCK_DATA = 'mock_data',
  CACHED_RESPONSE = 'cached_response',
  DEGRADED_FUNCTIONALITY = 'degraded_functionality',
  ALTERNATIVE_SERVICE = 'alternative_service',
  MANUAL_INTERVENTION = 'manual_intervention'
}

// Base error interface
export interface BaseError {
  code: string
  message: string
  category: ErrorCategory
  severity: ErrorSeverity
  timestamp: Date
  context?: Record<string, any>
  stack?: string
  retryable: boolean
  retryStrategy: RetryStrategy
  fallbackStrategy: FallbackStrategy
}

// Retry configuration
export interface RetryConfig {
  strategy: RetryStrategy
  maxAttempts: number
  baseDelay: number
  maxDelay: number
  jitter: boolean
  backoffMultiplier: number
  retryableErrors: string[]
  nonRetryableErrors: string[]
}

// Circuit breaker configuration
export interface CircuitBreakerConfig {
  failureThreshold: number
  recoveryTimeout: number
  monitoringPeriod: number
  halfOpenMaxCalls: number
  enabled: boolean
}

// Fallback configuration
export interface FallbackConfig {
  strategy: FallbackStrategy
  enabled: boolean
  timeout: number
  cacheKey?: string
  mockDataProvider?: () => any
  alternativeServiceUrl?: string
}

// Error handling configuration
export interface ErrorHandlingConfig {
  retry: RetryConfig
  circuitBreaker: CircuitBreakerConfig
  fallback: FallbackConfig
  monitoring: {
    enabled: boolean
    alertThreshold: number
    logLevel: 'debug' | 'info' | 'warn' | 'error'
  }
}

// Custom error classes
export class AppError extends Error implements BaseError {
  public readonly code: string
  public readonly category: ErrorCategory
  public readonly severity: ErrorSeverity
  public readonly timestamp: Date
  public readonly context?: Record<string, any>
  public readonly retryable: boolean
  public readonly retryStrategy: RetryStrategy
  public readonly fallbackStrategy: FallbackStrategy

  constructor(
    message: string,
    code: string,
    category: ErrorCategory,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    retryable: boolean = false,
    retryStrategy: RetryStrategy = RetryStrategy.NONE,
    fallbackStrategy: FallbackStrategy = FallbackStrategy.NONE,
    context?: Record<string, any>
  ) {
    super(message)
    this.name = 'AppError'
    this.code = code
    this.category = category
    this.severity = severity
    this.timestamp = new Date()
    this.context = context
    this.retryable = retryable
    this.retryStrategy = retryStrategy
    this.fallbackStrategy = fallbackStrategy

    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError)
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      category: this.category,
      severity: this.severity,
      timestamp: this.timestamp,
      context: this.context,
      retryable: this.retryable,
      retryStrategy: this.retryStrategy,
      fallbackStrategy: this.fallbackStrategy,
      stack: this.stack
    }
  }
}

// Specific error types
export class NetworkError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(
      message,
      'NETWORK_ERROR',
      ErrorCategory.NETWORK,
      ErrorSeverity.HIGH,
      true,
      RetryStrategy.EXPONENTIAL,
      FallbackStrategy.CACHED_RESPONSE,
      context
    )
    this.name = 'NetworkError'
  }
}

export class APIError extends AppError {
  constructor(message: string, statusCode?: number, context?: Record<string, any>) {
    const severity = statusCode && statusCode >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM
    const retryable = statusCode ? statusCode >= 500 || statusCode === 429 : false
    
    super(
      message,
      'API_ERROR',
      ErrorCategory.API,
      severity,
      retryable,
      retryable ? RetryStrategy.EXPONENTIAL : RetryStrategy.NONE,
      FallbackStrategy.MOCK_DATA,
      { ...context, statusCode }
    )
    this.name = 'APIError'
  }
}

export class RateLimitError extends AppError {
  constructor(message: string, retryAfter?: number, context?: Record<string, any>) {
    super(
      message,
      'RATE_LIMIT_ERROR',
      ErrorCategory.RATE_LIMIT,
      ErrorSeverity.MEDIUM,
      true,
      RetryStrategy.FIXED,
      FallbackStrategy.CACHED_RESPONSE,
      { ...context, retryAfter }
    )
    this.name = 'RateLimitError'
  }
}

export class TimeoutError extends AppError {
  constructor(message: string, timeout: number, context?: Record<string, any>) {
    super(
      message,
      'TIMEOUT_ERROR',
      ErrorCategory.TIMEOUT,
      ErrorSeverity.HIGH,
      true,
      RetryStrategy.LINEAR,
      FallbackStrategy.DEGRADED_FUNCTIONALITY,
      { ...context, timeout }
    )
    this.name = 'TimeoutError'
  }
}

export class ValidationError extends AppError {
  constructor(message: string, field?: string, context?: Record<string, any>) {
    super(
      message,
      'VALIDATION_ERROR',
      ErrorCategory.VALIDATION,
      ErrorSeverity.LOW,
      false,
      RetryStrategy.NONE,
      FallbackStrategy.NONE,
      { ...context, field }
    )
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string, context?: Record<string, any>) {
    super(
      message,
      'AUTHENTICATION_ERROR',
      ErrorCategory.AUTHENTICATION,
      ErrorSeverity.HIGH,
      false,
      RetryStrategy.NONE,
      FallbackStrategy.MANUAL_INTERVENTION,
      context
    )
    this.name = 'AuthenticationError'
  }
}

export class ExternalServiceError extends AppError {
  constructor(message: string, service: string, context?: Record<string, any>) {
    super(
      message,
      'EXTERNAL_SERVICE_ERROR',
      ErrorCategory.EXTERNAL_SERVICE,
      ErrorSeverity.HIGH,
      true,
      RetryStrategy.EXPONENTIAL,
      FallbackStrategy.ALTERNATIVE_SERVICE,
      { ...context, service }
    )
    this.name = 'ExternalServiceError'
  }
}

// Error factory for creating appropriate error types
export class ErrorFactory {
  static createFromHttpStatus(statusCode: number, message: string, context?: Record<string, any>): AppError {
    switch (statusCode) {
      case 400:
        return new ValidationError(message, undefined, context)
      case 401:
        return new AuthenticationError(message, context)
      case 403:
        return new AppError(message, 'AUTHORIZATION_ERROR', ErrorCategory.AUTHORIZATION, ErrorSeverity.HIGH, false, RetryStrategy.NONE, FallbackStrategy.MANUAL_INTERVENTION, context)
      case 404:
        return new AppError(message, 'NOT_FOUND_ERROR', ErrorCategory.API, ErrorSeverity.MEDIUM, false, RetryStrategy.NONE, FallbackStrategy.NONE, context)
      case 429:
        return new RateLimitError(message, undefined, context)
      case 500:
      case 502:
      case 503:
      case 504:
        return new APIError(message, statusCode, context)
      default:
        return new APIError(message, statusCode, context)
    }
  }

  static createNetworkError(message: string, context?: Record<string, any>): NetworkError {
    return new NetworkError(message, context)
  }

  static createTimeoutError(message: string, timeout: number, context?: Record<string, any>): TimeoutError {
    return new TimeoutError(message, timeout, context)
  }

  static createExternalServiceError(message: string, service: string, context?: Record<string, any>): ExternalServiceError {
    return new ExternalServiceError(message, service, context)
  }
}

// Type guards
export function isRetryableError(error: any): boolean {
  return error instanceof AppError && error.retryable
}

export function isNetworkError(error: any): error is NetworkError {
  return error instanceof NetworkError
}

export function isAPIError(error: any): error is APIError {
  return error instanceof APIError
}

export function isRateLimitError(error: any): error is RateLimitError {
  return error instanceof RateLimitError
}

export function isTimeoutError(error: any): error is TimeoutError {
  return error instanceof TimeoutError
}
