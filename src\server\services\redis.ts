import Redis from 'ioredis';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
}

interface SessionData {
  userId: string;
  email: string;
  role: string;
  lastActivity: number;
}

class RedisServiceClass {
  private client: Redis | null = null;
  private isConnected: boolean = false;

  async initialize(): Promise<void> {
    try {
      const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
      
      this.client = new Redis(redisUrl, {
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      });

      // Test the connection
      await this.client.connect();
      await this.client.ping();
      
      this.isConnected = true;
      console.log('✓ Redis connection established successfully');

      // Set up event listeners
      this.client.on('error', (error) => {
        console.error('Redis error:', error);
        this.isConnected = false;
      });

      this.client.on('connect', () => {
        console.log('Redis connected');
        this.isConnected = true;
      });

      this.client.on('disconnect', () => {
        console.log('Redis disconnected');
        this.isConnected = false;
      });

    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      // Don't throw error in development - allow app to continue without Redis
      if (process.env.NODE_ENV === 'production') {
        throw error;
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.quit();
      this.client = null;
      this.isConnected = false;
      console.log('Redis connection closed');
    }
  }

  get isReady(): boolean {
    return this.isConnected && this.client !== null;
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.client || !this.isConnected) {
        return false;
      }

      const result = await this.client.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('Redis health check failed:', error);
      return false;
    }
  }

  // Session management
  async setSession(sessionId: string, data: SessionData, ttl: number = 86400): Promise<boolean> {
    try {
      if (!this.isReady) {
        console.warn('Redis not available, skipping session storage');
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      await this.client!.setex(sessionKey, ttl, JSON.stringify(data));
      return true;
    } catch (error) {
      console.error('Failed to set session:', error);
      return false;
    }
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const sessionKey = `session:${sessionId}`;
      const data = await this.client!.get(sessionKey);
      
      if (!data) {
        return null;
      }

      return JSON.parse(data) as SessionData;
    } catch (error) {
      console.error('Failed to get session:', error);
      return null;
    }
  }

  async deleteSession(sessionId: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      const result = await this.client!.del(sessionKey);
      return result > 0;
    } catch (error) {
      console.error('Failed to delete session:', error);
      return false;
    }
  }

  async refreshSession(sessionId: string, ttl: number = 86400): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const sessionKey = `session:${sessionId}`;
      const result = await this.client!.expire(sessionKey, ttl);
      return result === 1;
    } catch (error) {
      console.error('Failed to refresh session:', error);
      return false;
    }
  }

  // Caching methods
  async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const serializedValue = JSON.stringify(value);
      
      if (options.ttl) {
        await this.client!.setex(key, options.ttl, serializedValue);
      } else {
        await this.client!.set(key, serializedValue);
      }
      
      return true;
    } catch (error) {
      console.error('Failed to set cache:', error);
      return false;
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const data = await this.client!.get(key);
      
      if (!data) {
        return null;
      }

      return JSON.parse(data) as T;
    } catch (error) {
      console.error('Failed to get cache:', error);
      return null;
    }
  }

  async del(key: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const result = await this.client!.del(key);
      return result > 0;
    } catch (error) {
      console.error('Failed to delete cache:', error);
      return false;
    }
  }

  async exists(key: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const result = await this.client!.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Failed to check cache existence:', error);
      return false;
    }
  }

  // Rate limiting
  async incrementCounter(key: string, ttl: number = 3600): Promise<number> {
    try {
      if (!this.isReady) {
        return 0;
      }

      const multi = this.client!.multi();
      multi.incr(key);
      multi.expire(key, ttl);

      const results = await multi.exec();
      return results?.[0]?.[1] as number || 0;
    } catch (error) {
      console.error('Failed to increment counter:', error);
      return 0;
    }
  }

  // ============================================================================
  // MULTI-STAGE PROCESSING CACHE METHODS
  // ============================================================================

  // Cache stage results for multi-stage processing
  async cacheStageResult(imageId: string, stage: string, result: any, ttl: number = 3600): Promise<boolean> {
    try {
      if (!this.isReady) {
        console.warn('Redis not available, skipping stage result caching');
        return false;
      }

      const cacheKey = `stage:${imageId}:${stage}`;
      const serializedResult = JSON.stringify({
        stage,
        imageId,
        result,
        timestamp: Date.now(),
        version: '1.0'
      });

      await this.client!.setex(cacheKey, ttl, serializedResult);
      console.log(`📦 Cached ${stage} results for image ${imageId} (TTL: ${ttl}s)`);
      return true;
    } catch (error) {
      console.error(`Failed to cache ${stage} results for image ${imageId}:`, error);
      return false;
    }
  }

  // Retrieve cached stage result
  async getStageResult(imageId: string, stage: string): Promise<any | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const cacheKey = `stage:${imageId}:${stage}`;
      const data = await this.client!.get(cacheKey);

      if (!data) {
        return null;
      }

      const parsed = JSON.parse(data);
      console.log(`📦 Retrieved cached ${stage} results for image ${imageId}`);
      return parsed.result;
    } catch (error) {
      console.error(`Failed to retrieve ${stage} results for image ${imageId}:`, error);
      return null;
    }
  }

  // Cache processed components with hierarchical structure
  async cacheProcessedComponents(imageId: string, components: any[], hierarchy: any, ttl: number = 7200): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const cacheKey = `components:${imageId}`;
      const componentData = {
        components,
        hierarchy,
        processedAt: Date.now(),
        version: '1.0'
      };

      await this.client!.setex(cacheKey, ttl, JSON.stringify(componentData));
      console.log(`📦 Cached ${components.length} components for image ${imageId}`);
      return true;
    } catch (error) {
      console.error(`Failed to cache components for image ${imageId}:`, error);
      return false;
    }
  }

  // Retrieve cached processed components
  async getProcessedComponents(imageId: string): Promise<{ components: any[], hierarchy: any } | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const cacheKey = `components:${imageId}`;
      const data = await this.client!.get(cacheKey);

      if (!data) {
        return null;
      }

      const parsed = JSON.parse(data);
      console.log(`📦 Retrieved cached components for image ${imageId}`);
      return {
        components: parsed.components,
        hierarchy: parsed.hierarchy
      };
    } catch (error) {
      console.error(`Failed to retrieve components for image ${imageId}:`, error);
      return null;
    }
  }

  // Cache design tokens (colors, typography, spacing)
  async cacheDesignTokens(imageId: string, tokens: any, ttl: number = 7200): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const cacheKey = `tokens:${imageId}`;
      const tokenData = {
        ...tokens,
        extractedAt: Date.now(),
        version: '1.0'
      };

      await this.client!.setex(cacheKey, ttl, JSON.stringify(tokenData));
      console.log(`📦 Cached design tokens for image ${imageId}`);
      return true;
    } catch (error) {
      console.error(`Failed to cache design tokens for image ${imageId}:`, error);
      return false;
    }
  }

  // Retrieve cached design tokens
  async getDesignTokens(imageId: string): Promise<any | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const cacheKey = `tokens:${imageId}`;
      const data = await this.client!.get(cacheKey);

      if (!data) {
        return null;
      }

      const parsed = JSON.parse(data);
      console.log(`📦 Retrieved cached design tokens for image ${imageId}`);
      return parsed;
    } catch (error) {
      console.error(`Failed to retrieve design tokens for image ${imageId}:`, error);
      return null;
    }
  }

  // Cache processing pipeline status
  async cachePipelineStatus(imageId: string, status: any, ttl: number = 1800): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const cacheKey = `pipeline:${imageId}`;
      const pipelineData = {
        ...status,
        lastUpdated: Date.now(),
        version: '1.0'
      };

      await this.client!.setex(cacheKey, ttl, JSON.stringify(pipelineData));
      return true;
    } catch (error) {
      console.error(`Failed to cache pipeline status for image ${imageId}:`, error);
      return false;
    }
  }

  // Retrieve pipeline status
  async getPipelineStatus(imageId: string): Promise<any | null> {
    try {
      if (!this.isReady) {
        return null;
      }

      const cacheKey = `pipeline:${imageId}`;
      const data = await this.client!.get(cacheKey);

      if (!data) {
        return null;
      }

      return JSON.parse(data);
    } catch (error) {
      console.error(`Failed to retrieve pipeline status for image ${imageId}:`, error);
      return null;
    }
  }

  // Batch operations for performance
  async batchCacheStageResults(operations: Array<{imageId: string, stage: string, result: any}>): Promise<boolean> {
    try {
      if (!this.isReady || operations.length === 0) {
        return false;
      }

      const multi = this.client!.multi();

      operations.forEach(({ imageId, stage, result }) => {
        const cacheKey = `stage:${imageId}:${stage}`;
        const serializedResult = JSON.stringify({
          stage,
          imageId,
          result,
          timestamp: Date.now(),
          version: '1.0'
        });
        multi.setex(cacheKey, 3600, serializedResult);
      });

      await multi.exec();
      console.log(`📦 Batch cached ${operations.length} stage results`);
      return true;
    } catch (error) {
      console.error('Failed to batch cache stage results:', error);
      return false;
    }
  }

  // Clean up expired cache entries for an image
  async cleanupImageCache(imageId: string): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      const patterns = [
        `stage:${imageId}:*`,
        `components:${imageId}`,
        `tokens:${imageId}`,
        `pipeline:${imageId}`
      ];

      let deletedCount = 0;
      for (const pattern of patterns) {
        const keys = await this.client!.keys(pattern);
        if (keys.length > 0) {
          const deleted = await this.client!.del(...keys);
          deletedCount += deleted;
        }
      }

      console.log(`🧹 Cleaned up ${deletedCount} cache entries for image ${imageId}`);
      return true;
    } catch (error) {
      console.error(`Failed to cleanup cache for image ${imageId}:`, error);
      return false;
    }
  }

  // Get cache statistics
  async getCacheStats(): Promise<any> {
    try {
      if (!this.isReady) {
        return null;
      }

      const info = await this.client!.info('memory');
      const keyspace = await this.client!.info('keyspace');

      // Count different types of cached data
      const stageKeys = await this.client!.keys('stage:*');
      const componentKeys = await this.client!.keys('components:*');
      const tokenKeys = await this.client!.keys('tokens:*');
      const pipelineKeys = await this.client!.keys('pipeline:*');

      return {
        memory: info,
        keyspace,
        counts: {
          stageResults: stageKeys.length,
          components: componentKeys.length,
          designTokens: tokenKeys.length,
          pipelines: pipelineKeys.length,
          total: stageKeys.length + componentKeys.length + tokenKeys.length + pipelineKeys.length
        }
      };
    } catch (error) {
      console.error('Failed to get cache stats:', error);
      return null;
    }
  }

  // Utility methods
  async flushAll(): Promise<boolean> {
    try {
      if (!this.isReady) {
        return false;
      }

      await this.client!.flushall();
      return true;
    } catch (error) {
      console.error('Failed to flush cache:', error);
      return false;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      if (!this.isReady) {
        return [];
      }

      return await this.client!.keys(pattern);
    } catch (error) {
      console.error('Failed to get keys:', error);
      return [];
    }
  }
}

export const RedisService = new RedisServiceClass();
