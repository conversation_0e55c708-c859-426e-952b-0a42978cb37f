import { describe, it, expect, beforeAll, afterAll, beforeEach } from '@jest/globals';
import { RedisService } from '../server/services/redis';
import { DatabaseService } from '../server/services/database';
import { AnalysisStorageService } from '../server/services/analysis-storage';
import { S3Service } from '../server/services/s3';

describe('Advanced Result Storage and Caching', () => {
  beforeAll(async () => {
    // Initialize services
    await RedisService.initialize();
    await DatabaseService.initialize();
    await S3Service.initialize();
  });

  afterAll(async () => {
    // Cleanup
    await RedisService.disconnect();
    await DatabaseService.disconnect();
  });

  beforeEach(async () => {
    // Clean up test data before each test
    await RedisService.flushAll();
  });

  describe('Redis Caching Enhancement', () => {
    const testImageId = 'test-image-123';
    const testStage = 'component-detection';
    const testResult = {
      components: [
        { id: 1, type: 'button', confidence: 0.95 },
        { id: 2, type: 'input', confidence: 0.88 }
      ],
      metadata: { processingTime: 1500 }
    };

    it('should cache and retrieve stage results', async () => {
      // Cache stage result
      const cached = await RedisService.cacheStageResult(testImageId, testStage, testResult, 3600);
      expect(cached).toBe(true);

      // Retrieve stage result
      const retrieved = await RedisService.getStageResult(testImageId, testStage);
      expect(retrieved).toEqual(expect.objectContaining({
        stage: testStage,
        imageId: testImageId,
        result: testResult
      }));
    });

    it('should cache and retrieve processed components', async () => {
      const components = [
        { id: 1, type: 'button', x: 10, y: 20, width: 100, height: 40 },
        { id: 2, type: 'input', x: 10, y: 80, width: 200, height: 30 }
      ];
      const hierarchy = { root: { children: [1, 2] } };

      // Cache components
      const cached = await RedisService.cacheProcessedComponents(testImageId, components, hierarchy, 7200);
      expect(cached).toBe(true);

      // Retrieve components
      const retrieved = await RedisService.getProcessedComponents(testImageId);
      expect(retrieved).toEqual({
        components,
        hierarchy
      });
    });

    it('should cache and retrieve design tokens', async () => {
      const designTokens = {
        colors: { primary: '#007bff', secondary: '#6c757d' },
        typography: { fontFamily: 'Arial', fontSize: '16px' },
        spacing: { small: '8px', medium: '16px' },
        components: { button: { padding: '8px 16px' } },
        breakpoints: { mobile: '768px', desktop: '1024px' }
      };

      // Cache design tokens
      const cached = await RedisService.cacheDesignTokens(testImageId, designTokens, 7200);
      expect(cached).toBe(true);

      // Retrieve design tokens
      const retrieved = await RedisService.getDesignTokens(testImageId);
      expect(retrieved).toEqual(designTokens);
    });

    it('should handle pipeline status caching', async () => {
      const pipelineStatus = {
        currentStage: 3,
        totalStages: 6,
        progress: 50,
        status: 'RUNNING',
        startedAt: new Date().toISOString()
      };

      // Cache pipeline status
      const cached = await RedisService.cachePipelineStatus(testImageId, pipelineStatus, 1800);
      expect(cached).toBe(true);

      // Retrieve pipeline status
      const retrieved = await RedisService.getPipelineStatus(testImageId);
      expect(retrieved).toEqual(expect.objectContaining(pipelineStatus));
    });

    it('should perform batch cache operations', async () => {
      const operations = [
        { imageId: 'img1', stage: 'preprocessing', result: { status: 'complete' } },
        { imageId: 'img2', stage: 'component-detection', result: { components: [] } },
        { imageId: 'img3', stage: 'layout-analysis', result: { layout: {} } }
      ];

      // Batch cache operations
      const success = await RedisService.batchCacheStageResults(operations);
      expect(success).toBe(true);

      // Verify all operations were cached
      for (const op of operations) {
        const retrieved = await RedisService.getStageResult(op.imageId, op.stage);
        expect(retrieved?.result).toEqual(op.result);
      }
    });

    it('should cleanup image cache', async () => {
      // Cache multiple items for the test image
      await RedisService.cacheStageResult(testImageId, 'stage1', { data: 'test1' });
      await RedisService.cacheStageResult(testImageId, 'stage2', { data: 'test2' });
      await RedisService.cacheProcessedComponents(testImageId, [], {});
      await RedisService.cacheDesignTokens(testImageId, {}, 3600);

      // Cleanup
      const cleaned = await RedisService.cleanupImageCache(testImageId);
      expect(cleaned).toBe(true);

      // Verify cleanup
      const stage1 = await RedisService.getStageResult(testImageId, 'stage1');
      const stage2 = await RedisService.getStageResult(testImageId, 'stage2');
      const components = await RedisService.getProcessedComponents(testImageId);
      const tokens = await RedisService.getDesignTokens(testImageId);

      expect(stage1).toBeNull();
      expect(stage2).toBeNull();
      expect(components).toBeNull();
      expect(tokens).toBeNull();
    });

    it('should provide cache statistics', async () => {
      // Cache some test data
      await RedisService.cacheStageResult('img1', 'stage1', { data: 'test' });
      await RedisService.cacheProcessedComponents('img2', [], {});
      await RedisService.cacheDesignTokens('img3', {}, 3600);

      // Get cache stats
      const stats = await RedisService.getCacheStats();
      expect(stats).toHaveProperty('counts');
      expect(stats.counts).toHaveProperty('stageResults');
      expect(stats.counts).toHaveProperty('components');
      expect(stats.counts).toHaveProperty('designTokens');
      expect(stats.counts).toHaveProperty('total');
      expect(stats.counts.total).toBeGreaterThan(0);
    });
  });

  describe('Database Schema Optimization', () => {
    const testUserId = 'test-user-123';
    const testImageId = 'test-image-456';

    it('should store and retrieve analysis results', async () => {
      const analysisData = {
        imageId: testImageId,
        userId: testUserId,
        analysisType: 'COMPONENT_DETECTION',
        processingMode: 'MULTI_STAGE',
        results: { components: [], confidence: 0.9 },
        confidence: 0.9,
        processingTime: 2500,
        componentCount: 5,
        qualityScore: 85
      };

      // Store analysis result
      const resultId = await AnalysisStorageService.storeAnalysisResult(analysisData);
      expect(resultId).toBeTruthy();

      // Retrieve analysis result
      const retrieved = await AnalysisStorageService.getAnalysisResult(testImageId, 'COMPONENT_DETECTION');
      expect(retrieved).toEqual(analysisData.results);
    });

    it('should store and retrieve component detection', async () => {
      const componentData = {
        imageId: testImageId,
        userId: testUserId,
        components: [
          { id: 1, type: 'button', confidence: 0.95 },
          { id: 2, type: 'input', confidence: 0.88 }
        ],
        hierarchy: { root: { children: [1, 2] } },
        boundingBoxes: [
          { id: 1, x: 10, y: 20, width: 100, height: 40 },
          { id: 2, x: 10, y: 80, width: 200, height: 30 }
        ],
        confidence: 0.91,
        processingTime: 1800
      };

      // Store component detection
      const detectionId = await AnalysisStorageService.storeComponentDetection(componentData);
      expect(detectionId).toBeTruthy();

      // Retrieve component detection
      const retrieved = await AnalysisStorageService.getComponentDetection(testImageId);
      expect(retrieved).toEqual({
        components: componentData.components,
        hierarchy: componentData.hierarchy
      });
    });

    it('should store and retrieve design tokens', async () => {
      const tokenData = {
        imageId: testImageId,
        userId: testUserId,
        colors: { primary: '#007bff', secondary: '#6c757d' },
        typography: { fontFamily: 'Arial', fontSize: '16px' },
        spacing: { small: '8px', medium: '16px' },
        components: { button: { padding: '8px 16px' } },
        breakpoints: { mobile: '768px', desktop: '1024px' },
        confidence: 0.87,
        tokenCount: 8
      };

      // Store design tokens
      const tokenId = await AnalysisStorageService.storeDesignTokens(tokenData);
      expect(tokenId).toBeTruthy();

      // Retrieve design tokens
      const retrieved = await AnalysisStorageService.getDesignTokens(testImageId);
      expect(retrieved).toEqual({
        colors: tokenData.colors,
        typography: tokenData.typography,
        spacing: tokenData.spacing,
        components: tokenData.components,
        breakpoints: tokenData.breakpoints
      });
    });

    it('should perform batch storage operations', async () => {
      const allResults = {
        componentDetection: {
          components: [{ id: 1, type: 'button' }],
          hierarchy: { root: { children: [1] } },
          boundingBoxes: [{ id: 1, x: 0, y: 0, width: 100, height: 40 }],
          confidence: 0.9,
          processingTime: 1500
        },
        designTokens: {
          colors: { primary: '#007bff' },
          typography: { fontFamily: 'Arial' },
          spacing: { small: '8px' },
          components: { button: {} },
          breakpoints: { mobile: '768px' },
          confidence: 0.85,
          tokenCount: 5
        },
        layoutAnalysis: { patterns: [], relationships: [] },
        qualityAssessment: { score: 88, metrics: {} }
      };

      // Batch store results
      const success = await AnalysisStorageService.batchStoreResults(testImageId, testUserId, allResults);
      expect(success).toBe(true);

      // Verify all results were stored
      const components = await AnalysisStorageService.getComponentDetection(testImageId);
      const tokens = await AnalysisStorageService.getDesignTokens(testImageId);
      const layout = await AnalysisStorageService.getAnalysisResult(testImageId, 'LAYOUT_ANALYSIS');
      const quality = await AnalysisStorageService.getAnalysisResult(testImageId, 'QUALITY_ASSESSMENT');

      expect(components).toBeTruthy();
      expect(tokens).toBeTruthy();
      expect(layout).toBeTruthy();
      expect(quality).toBeTruthy();
    });

    it('should cleanup analysis data', async () => {
      // Store some test data first
      await AnalysisStorageService.storeAnalysisResult({
        imageId: testImageId,
        userId: testUserId,
        analysisType: 'TEST_ANALYSIS',
        processingMode: 'STANDARD',
        results: { test: 'data' }
      });

      // Cleanup
      const cleaned = await AnalysisStorageService.cleanupImageAnalysis(testImageId);
      expect(cleaned).toBe(true);

      // Verify cleanup
      const retrieved = await AnalysisStorageService.getAnalysisResult(testImageId, 'TEST_ANALYSIS');
      expect(retrieved).toBeNull();
    });
  });

  describe('S3 Storage for Generated Assets', () => {
    it('should upload generated assets', async () => {
      const testBuffer = Buffer.from('test file content');
      const filename = 'test-asset.txt';
      const contentType = 'text/plain';

      const result = await S3Service.uploadGeneratedAsset(testBuffer, filename, contentType, 'test-folder');
      expect(result).toBeTruthy();
      expect(result?.key).toContain('test-folder');
      expect(result?.key).toContain(filename);
      expect(result?.url).toBeTruthy();
    });

    it('should upload analysis reports', async () => {
      const reportData = {
        imageId: 'test-123',
        analysis: { components: [], tokens: {} },
        summary: 'Test analysis report',
        generatedAt: new Date().toISOString()
      };

      const result = await S3Service.uploadAnalysisReport(reportData, 'test-123');
      expect(result).toBeTruthy();
      expect(result?.key).toContain('analysis-report');
      expect(result?.url).toBeTruthy();
    });

    it('should get generated asset URLs', async () => {
      const testKey = 'generated/test-asset.txt';
      const url = await S3Service.getGeneratedAssetUrl(testKey);
      expect(url).toBeTruthy();
      expect(url).toContain(testKey);
    });
  });

  describe('Integration Tests', () => {
    it('should handle cache-database fallback correctly', async () => {
      const testImageId = 'integration-test-123';
      const testUserId = 'user-456';
      
      // Store in database only (not in cache)
      await DatabaseService.createComponentDetection({
        imageId: testImageId,
        userId: testUserId,
        components: JSON.stringify([{ id: 1, type: 'button' }]),
        hierarchy: JSON.stringify({ root: { children: [1] } }),
        boundingBoxes: JSON.stringify([{ id: 1, x: 0, y: 0, width: 100, height: 40 }]),
        confidence: 0.9,
        processingTime: 1500
      });

      // Retrieve through AnalysisStorageService (should fallback to database and cache result)
      const retrieved = await AnalysisStorageService.getComponentDetection(testImageId);
      expect(retrieved).toBeTruthy();
      expect(retrieved?.components).toHaveLength(1);

      // Verify it was cached
      const cached = await RedisService.getProcessedComponents(testImageId);
      expect(cached).toBeTruthy();
    });
  });
});
