/**
 * Error Monitoring and Alerting System
 * 
 * Provides comprehensive error tracking, logging, and alerting
 * for production monitoring and debugging.
 */

import { 
  AppError, 
  ErrorCategory, 
  ErrorSeverity,
  RetryResult 
} from './types'
import { CircuitBreakerEvent } from './circuit-breaker'
import { FallbackResult } from './fallback-manager'

// Error event types
export interface ErrorEvent {
  id: string
  timestamp: Date
  error: AppError | Error
  context: {
    operationName: string
    userId?: string
    requestId?: string
    userAgent?: string
    ip?: string
    metadata?: Record<string, any>
  }
  severity: ErrorSeverity
  category: ErrorCategory
  resolved: boolean
  retryAttempts?: number
  fallbackUsed?: boolean
  circuitBreakerTriggered?: boolean
}

// Error statistics
export interface ErrorStats {
  totalErrors: number
  errorsByCategory: Record<ErrorCategory, number>
  errorsBySeverity: Record<ErrorSeverity, number>
  errorsByHour: Record<string, number>
  topErrors: Array<{ code: string; count: number; lastOccurrence: Date }>
  averageResolutionTime: number
  circuitBreakerTrips: number
  fallbackActivations: number
}

// Alert configuration
export interface AlertConfig {
  enabled: boolean
  thresholds: {
    errorRate: number // errors per minute
    criticalErrors: number // critical errors per hour
    circuitBreakerTrips: number // trips per hour
    fallbackActivations: number // activations per hour
  }
  channels: {
    console: boolean
    email: boolean
    webhook: boolean
    slack: boolean
  }
  webhookUrl?: string
  emailRecipients?: string[]
  slackWebhookUrl?: string
}

// Alert message
export interface AlertMessage {
  id: string
  timestamp: Date
  type: 'error_rate' | 'critical_error' | 'circuit_breaker' | 'fallback' | 'system_health'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  data: Record<string, any>
  acknowledged: boolean
}

export class ErrorMonitor {
  private events: ErrorEvent[] = []
  private alerts: AlertMessage[] = []
  private stats: ErrorStats
  private alertConfig: AlertConfig
  private monitoringInterval?: NodeJS.Timeout
  private lastAlertCheck: Date = new Date()

  private defaultAlertConfig: AlertConfig = {
    enabled: true,
    thresholds: {
      errorRate: 10, // 10 errors per minute
      criticalErrors: 5, // 5 critical errors per hour
      circuitBreakerTrips: 3, // 3 trips per hour
      fallbackActivations: 10 // 10 activations per hour
    },
    channels: {
      console: true,
      email: false,
      webhook: false,
      slack: false
    }
  }

  constructor(alertConfig: Partial<AlertConfig> = {}) {
    this.alertConfig = { ...this.defaultAlertConfig, ...alertConfig }
    this.stats = this.initializeStats()
    this.startMonitoring()
    
    console.log('🔍 Error monitoring system initialized')
  }

  /**
   * Record an error event
   */
  recordError(
    error: AppError | Error,
    context: Partial<ErrorEvent['context']> = {}
  ): string {
    const errorEvent: ErrorEvent = {
      id: this.generateId(),
      timestamp: new Date(),
      error,
      context: {
        operationName: 'unknown',
        ...context
      },
      severity: error instanceof AppError ? error.severity : ErrorSeverity.MEDIUM,
      category: error instanceof AppError ? error.category : ErrorCategory.INTERNAL,
      resolved: false
    }

    this.events.push(errorEvent)
    this.updateStats(errorEvent)
    
    // Keep only last 1000 events to prevent memory issues
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000)
    }

    console.error(`📊 Error recorded [${errorEvent.id}]:`, {
      message: error.message,
      category: errorEvent.category,
      severity: errorEvent.severity,
      operation: errorEvent.context.operationName
    })

    return errorEvent.id
  }

  /**
   * Record retry result
   */
  recordRetryResult(result: RetryResult<any>, operationName: string): void {
    if (!result.success && result.error) {
      const eventId = this.recordError(result.error, { 
        operationName,
        metadata: { 
          totalAttempts: result.totalAttempts,
          totalElapsed: result.totalElapsed,
          retryUsed: true
        }
      })

      // Update event with retry information
      const event = this.events.find(e => e.id === eventId)
      if (event) {
        event.retryAttempts = result.totalAttempts
      }
    }
  }

  /**
   * Record fallback result
   */
  recordFallbackResult(result: FallbackResult<any>, operationName: string): void {
    if (result.originalError) {
      const eventId = this.recordError(result.originalError, {
        operationName,
        metadata: {
          fallbackStrategy: result.strategy,
          fallbackSource: result.source,
          fallbackUsed: true
        }
      })

      // Update event with fallback information
      const event = this.events.find(e => e.id === eventId)
      if (event) {
        event.fallbackUsed = true
      }

      this.stats.fallbackActivations++
    }
  }

  /**
   * Record circuit breaker event
   */
  recordCircuitBreakerEvent(event: CircuitBreakerEvent, breakerName: string): void {
    if (event.type === 'state_change' && event.state === 'open') {
      this.stats.circuitBreakerTrips++
      
      const errorEvent: ErrorEvent = {
        id: this.generateId(),
        timestamp: event.timestamp,
        error: new AppError(
          `Circuit breaker '${breakerName}' opened`,
          'CIRCUIT_BREAKER_OPEN',
          ErrorCategory.EXTERNAL_SERVICE,
          ErrorSeverity.HIGH
        ),
        context: {
          operationName: `circuit-breaker-${breakerName}`,
          metadata: { breakerName, previousState: event.previousState }
        },
        severity: ErrorSeverity.HIGH,
        category: ErrorCategory.EXTERNAL_SERVICE,
        resolved: false,
        circuitBreakerTriggered: true
      }

      this.events.push(errorEvent)
      this.updateStats(errorEvent)
    }
  }

  /**
   * Get error statistics
   */
  getStats(): ErrorStats {
    return { ...this.stats }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 50): ErrorEvent[] {
    return this.events
      .slice(-limit)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
  }

  /**
   * Get errors by category
   */
  getErrorsByCategory(category: ErrorCategory): ErrorEvent[] {
    return this.events.filter(event => event.category === category)
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity): ErrorEvent[] {
    return this.events.filter(event => event.severity === severity)
  }

  /**
   * Mark error as resolved
   */
  resolveError(errorId: string): boolean {
    const event = this.events.find(e => e.id === errorId)
    if (event) {
      event.resolved = true
      console.log(`✅ Error ${errorId} marked as resolved`)
      return true
    }
    return false
  }

  /**
   * Get health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    errorRate: number
    criticalErrors: number
    circuitBreakerTrips: number
    fallbackActivations: number
    lastCheck: Date
  } {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000)

    const recentErrors = this.events.filter(e => e.timestamp >= oneMinuteAgo)
    const criticalErrors = this.events.filter(
      e => e.timestamp >= oneHourAgo && e.severity === ErrorSeverity.CRITICAL
    )

    const errorRate = recentErrors.length
    const criticalErrorCount = criticalErrors.length

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'
    
    if (criticalErrorCount >= this.alertConfig.thresholds.criticalErrors ||
        errorRate >= this.alertConfig.thresholds.errorRate) {
      status = 'unhealthy'
    } else if (criticalErrorCount > 0 || errorRate > this.alertConfig.thresholds.errorRate / 2) {
      status = 'degraded'
    }

    return {
      status,
      errorRate,
      criticalErrors: criticalErrorCount,
      circuitBreakerTrips: this.stats.circuitBreakerTrips,
      fallbackActivations: this.stats.fallbackActivations,
      lastCheck: now
    }
  }

  /**
   * Start monitoring and alerting
   */
  private startMonitoring(): void {
    if (this.alertConfig.enabled) {
      this.monitoringInterval = setInterval(() => {
        this.checkAlertThresholds()
      }, 60000) // Check every minute
    }
  }

  /**
   * Stop monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = undefined
    }
  }

  /**
   * Check alert thresholds
   */
  private checkAlertThresholds(): void {
    const health = this.getHealthStatus()
    
    // Check error rate threshold
    if (health.errorRate >= this.alertConfig.thresholds.errorRate) {
      this.sendAlert({
        type: 'error_rate',
        severity: 'high',
        title: 'High Error Rate Detected',
        message: `Error rate of ${health.errorRate} errors/minute exceeds threshold of ${this.alertConfig.thresholds.errorRate}`,
        data: { errorRate: health.errorRate, threshold: this.alertConfig.thresholds.errorRate }
      })
    }

    // Check critical errors threshold
    if (health.criticalErrors >= this.alertConfig.thresholds.criticalErrors) {
      this.sendAlert({
        type: 'critical_error',
        severity: 'critical',
        title: 'Critical Errors Detected',
        message: `${health.criticalErrors} critical errors in the last hour`,
        data: { criticalErrors: health.criticalErrors, threshold: this.alertConfig.thresholds.criticalErrors }
      })
    }

    // Check circuit breaker trips
    if (health.circuitBreakerTrips >= this.alertConfig.thresholds.circuitBreakerTrips) {
      this.sendAlert({
        type: 'circuit_breaker',
        severity: 'high',
        title: 'Circuit Breaker Trips',
        message: `${health.circuitBreakerTrips} circuit breaker trips in the last hour`,
        data: { trips: health.circuitBreakerTrips, threshold: this.alertConfig.thresholds.circuitBreakerTrips }
      })
    }
  }

  /**
   * Send alert
   */
  private sendAlert(alertData: Omit<AlertMessage, 'id' | 'timestamp' | 'acknowledged'>): void {
    const alert: AlertMessage = {
      id: this.generateId(),
      timestamp: new Date(),
      acknowledged: false,
      ...alertData
    }

    this.alerts.push(alert)

    // Send to configured channels
    if (this.alertConfig.channels.console) {
      console.error(`🚨 ALERT [${alert.severity.toUpperCase()}]: ${alert.title}`, alert.message)
    }

    // Additional channels would be implemented here
    // (email, webhook, Slack, etc.)
  }

  /**
   * Initialize statistics
   */
  private initializeStats(): ErrorStats {
    return {
      totalErrors: 0,
      errorsByCategory: {} as Record<ErrorCategory, number>,
      errorsBySeverity: {} as Record<ErrorSeverity, number>,
      errorsByHour: {},
      topErrors: [],
      averageResolutionTime: 0,
      circuitBreakerTrips: 0,
      fallbackActivations: 0
    }
  }

  /**
   * Update statistics
   */
  private updateStats(event: ErrorEvent): void {
    this.stats.totalErrors++
    
    // Update category stats
    this.stats.errorsByCategory[event.category] = 
      (this.stats.errorsByCategory[event.category] || 0) + 1
    
    // Update severity stats
    this.stats.errorsBySeverity[event.severity] = 
      (this.stats.errorsBySeverity[event.severity] || 0) + 1
    
    // Update hourly stats
    const hour = event.timestamp.toISOString().slice(0, 13)
    this.stats.errorsByHour[hour] = (this.stats.errorsByHour[hour] || 0) + 1
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// Singleton instance
export const errorMonitor = new ErrorMonitor()

// Utility functions
export function recordError(error: AppError | Error, context?: Partial<ErrorEvent['context']>): string {
  return errorMonitor.recordError(error, context)
}

export function getErrorStats(): ErrorStats {
  return errorMonitor.getStats()
}

export function getHealthStatus() {
  return errorMonitor.getHealthStatus()
}
